---
title: Tailwind CSS 404 Not Found Page - Flowbite
description: Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more.
layout: main
group: status
page: 404
---

<section class="mx-auto flex h-screen flex-col items-center justify-center px-6 dark:bg-gray-900 xl:px-0">
  <div class="block md:max-w-lg">
    <img class="mx-auto mb-4 hidden lg:flex" src="../../images/404.svg" alt="illustration" />
  </div>
  <div class="text-center xl:max-w-4xl">
    <h1 class="mb-4 text-2xl font-bold leading-tight text-primary-700 dark:text-primary-500">404 Not Found</h1>
    <p class="mb-5 text-2xl font-bold text-gray-900 dark:text-white md:text-4xl">Whoops! That page doesn’t exist.</p>
    <a
      href="{{< ref "/" >}}"
      class="mr-3 inline-flex items-center rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    >
      <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
      Go back home
    </a>
    <p class="my-4 text-gray-500 dark:text-gray-400 md:mt-8">Here are some helpful links instead:</p>
    <ul class="flex items-center justify-center space-x-4 text-gray-500 dark:text-gray-400">
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Home</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Search</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Help</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Contact</a>
      </li>
    </ul>
  </div>
</section>
