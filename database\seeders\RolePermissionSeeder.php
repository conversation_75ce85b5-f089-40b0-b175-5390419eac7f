<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User Management
            ['name' => 'users.view', 'group' => 'User Management', 'description' => 'View users'],
            ['name' => 'users.create', 'group' => 'User Management', 'description' => 'Create users'],
            ['name' => 'users.edit', 'group' => 'User Management', 'description' => 'Edit users'],
            ['name' => 'users.delete', 'group' => 'User Management', 'description' => 'Delete users'],
            ['name' => 'users.export', 'group' => 'User Management', 'description' => 'Export users'],
            ['name' => 'users.import', 'group' => 'User Management', 'description' => 'Import users'],

            // Role Management
            ['name' => 'roles.view', 'group' => 'Role Management', 'description' => 'View roles'],
            ['name' => 'roles.create', 'group' => 'Role Management', 'description' => 'Create roles'],
            ['name' => 'roles.edit', 'group' => 'Role Management', 'description' => 'Edit roles'],
            ['name' => 'roles.delete', 'group' => 'Role Management', 'description' => 'Delete roles'],
            ['name' => 'roles.assign', 'group' => 'Role Management', 'description' => 'Assign roles to users'],

            // Permission Management
            ['name' => 'permissions.view', 'group' => 'Permission Management', 'description' => 'View permissions'],
            ['name' => 'permissions.create', 'group' => 'Permission Management', 'description' => 'Create permissions'],
            ['name' => 'permissions.edit', 'group' => 'Permission Management', 'description' => 'Edit permissions'],
            ['name' => 'permissions.delete', 'group' => 'Permission Management', 'description' => 'Delete permissions'],
            ['name' => 'permissions.assign', 'group' => 'Permission Management', 'description' => 'Assign permissions'],

            // Dashboard
            ['name' => 'dashboard.view', 'group' => 'Dashboard', 'description' => 'View dashboard'],
            ['name' => 'dashboard.analytics', 'group' => 'Dashboard', 'description' => 'View analytics'],

            // Settings
            ['name' => 'settings.view', 'group' => 'Settings', 'description' => 'View settings'],
            ['name' => 'settings.edit', 'group' => 'Settings', 'description' => 'Edit settings'],

            // Activity Logs
            ['name' => 'activity-logs.view', 'group' => 'Activity Logs', 'description' => 'View activity logs'],
            ['name' => 'activity-logs.delete', 'group' => 'Activity Logs', 'description' => 'Delete activity logs'],

            // Profile
            ['name' => 'profile.view', 'group' => 'Profile', 'description' => 'View own profile'],
            ['name' => 'profile.edit', 'group' => 'Profile', 'description' => 'Edit own profile'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // Create roles
        $roles = [
            [
                'name' => 'Super Admin',
                'description' => 'Has access to all features and settings',
                'level' => 100,
                'is_default' => false,
            ],
            [
                'name' => 'Admin',
                'description' => 'Has access to most features except system settings',
                'level' => 80,
                'is_default' => false,
            ],
            [
                'name' => 'Manager',
                'description' => 'Can manage users and view reports',
                'level' => 60,
                'is_default' => false,
            ],
            [
                'name' => 'User',
                'description' => 'Basic user with limited access',
                'level' => 20,
                'is_default' => true,
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );

            // Assign permissions based on role
            $this->assignPermissionsToRole($role);
        }
    }

    /**
     * Assign permissions to roles based on their level.
     */
    private function assignPermissionsToRole(Role $role): void
    {
        $allPermissions = Permission::all();

        switch ($role->name) {
            case 'Super Admin':
                // Super Admin gets all permissions
                $role->permissions()->sync($allPermissions->pluck('id'));
                break;

            case 'Admin':
                // Admin gets most permissions except system settings
                $permissions = $allPermissions->whereNotIn('name', [
                    'settings.edit',
                ])->pluck('id');
                $role->permissions()->sync($permissions);
                break;

            case 'Manager':
                // Manager gets user management and viewing permissions
                $permissions = $allPermissions->whereIn('name', [
                    'dashboard.view',
                    'dashboard.analytics',
                    'users.view',
                    'users.create',
                    'users.edit',
                    'users.export',
                    'roles.view',
                    'activity-logs.view',
                    'profile.view',
                    'profile.edit',
                ])->pluck('id');
                $role->permissions()->sync($permissions);
                break;

            case 'User':
                // User gets basic permissions
                $permissions = $allPermissions->whereIn('name', [
                    'dashboard.view',
                    'profile.view',
                    'profile.edit',
                ])->pluck('id');
                $role->permissions()->sync($permissions);
                break;
        }
    }
}
