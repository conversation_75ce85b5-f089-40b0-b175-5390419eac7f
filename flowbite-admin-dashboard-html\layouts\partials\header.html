<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="description" content="{{ .Page.Params.description | default .Site.Params.description | markdownify }}" />
<meta name="author" content="{{ .Site.Params.authors }}" />
<meta name="generator" content="Hugo {{ hugo.Version }}" />

<title>{{ .Params.Title | markdownify }}</title>

<link rel="canonical" href="{{ .Permalink }}" />

{{ with .Params.robots -}}
  <meta name="robots" content="{{ . }}" />
{{- end }}

{{ partial "stylesheet" . }}
{{ partial "favicons" . }}
{{ partial "social" . }}
{{ partial "analytics" . }}

<script>
  // On page load or when changing themes, best to add inline in `head` to avoid FOUC
  if (localStorage.getItem("color-theme") === "dark" || (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
    document.documentElement.classList.add("dark");
  } else {
    document.documentElement.classList.remove("dark");
  }
</script>
