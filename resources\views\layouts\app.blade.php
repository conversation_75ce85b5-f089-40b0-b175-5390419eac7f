<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Dashboard')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom SweetAlert Styles -->
    <style>
        .swal2-confirm {
            background-color: #3b82f6 !important;
            border-color: #3b82f6 !important;
        }
        .swal2-confirm:hover {
            background-color: #2563eb !important;
            border-color: #2563eb !important;
        }
        .swal2-cancel {
            background-color: #6b7280 !important;
            border-color: #6b7280 !important;
        }
        .swal2-cancel:hover {
            background-color: #4b5563 !important;
            border-color: #4b5563 !important;
        }
    </style>

    <!-- Dark Mode Script -->
    <script>
        if (localStorage.getItem("color-theme") === "dark" || (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    @stack('styles')
</head>
<body class="bg-gray-50 dark:bg-gray-900 antialiased">
    <div id="app">
        @auth
            @include('layouts.header')
            @include('layouts.navigation')
            
            <!-- Main Content -->
            <div class="flex overflow-hidden bg-gray-50 pt-[62px] dark:bg-gray-900">
                <div id="main-content" class="relative h-full w-full overflow-x-scroll bg-gray-50 dark:bg-gray-900 lg:ms-64">
                    <main>
                        @yield('content')
                    </main>
                </div>
            </div>
        @else
            @yield('content')
        @endauth
    </div>

    <!-- Global SweetAlert Handler -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Global success message handler
            @if(session('success'))
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    confirmButtonColor: '#3B82F6',
                    timer: 3000,
                    timerProgressBar: true
                });
            @endif

            // Global error message handler
            @if(session('error'))
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: '{{ session('error') }}',
                    confirmButtonColor: '#3B82F6'
                });
            @endif
        });
    </script>
    
    @stack('scripts')

    <!-- Flowbite JS -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>
</body>
</html>
