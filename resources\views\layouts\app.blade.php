<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full dark">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - @yield('title', 'Dashboard')</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet" />

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <!-- Custom SweetAlert Styles -->
    <style>
        .swal2-confirm {
            background-color: #3b82f6 !important;
            border-color: #3b82f6 !important;
        }
        .swal2-confirm:hover {
            background-color: #2563eb !important;
            border-color: #2563eb !important;
        }
        .swal2-cancel {
            background-color: #6b7280 !important;
            border-color: #6b7280 !important;
        }
        .swal2-cancel:hover {
            background-color: #4b5563 !important;
            border-color: #4b5563 !important;
        }
    </style>

    <!-- Dark Mode Script -->
    <script>
        if (localStorage.getItem("color-theme") === "dark" || (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
            document.documentElement.classList.add("dark");
        } else {
            document.documentElement.classList.remove("dark");
        }
    </script>
    
    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    @stack('styles')
</head>
<body class="bg-gray-50 dark:bg-gray-900 antialiased">
    <div id="app">
        @auth
            @include('layouts.header')
            @include('layouts.navigation')
            
            <!-- Main Content -->
            <div class="flex overflow-hidden bg-gray-50 pt-[62px] dark:bg-gray-900">
                <div id="main-content" class="relative h-full w-full overflow-x-scroll bg-gray-50 dark:bg-gray-900 lg:ms-64">
                    <main>
                        @yield('content')
                    </main>
                </div>
            </div>
        @else
            @yield('content')
        @endauth
    </div>

    <!-- Global SweetAlert Handler -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Global success message handler
            @if(session('success'))
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    confirmButtonColor: '#3B82F6',
                    timer: 3000,
                    timerProgressBar: true
                });
            @endif

            // Global error message handler
            @if(session('error'))
                Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: '{{ session('error') }}',
                    confirmButtonColor: '#3B82F6'
                });
            @endif
        });
    </script>
    
    @stack('scripts')

    <!-- Flowbite JS -->
    <script src="https://cdn.jsdelivr.net/npm/flowbite@3.1.2/dist/flowbite.min.js"></script>

    <!-- Sidebar functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing sidebar...');

            // Wait for Flowbite to be loaded and initialized
            setTimeout(() => {
                const sidebar = document.getElementById('sidebar');
                const toggleButton = document.getElementById('toggleSidebarButton');
                const mainContent = document.getElementById('main-content');

                console.log('Sidebar element:', sidebar);
                console.log('Toggle button:', toggleButton);
                console.log('Main content:', mainContent);

                if (sidebar && toggleButton && mainContent) {
                    console.log('All sidebar elements found, setting up...');

                    // Force show sidebar on desktop
                    function ensureDesktopSidebar() {
                        if (window.innerWidth >= 1024) { // lg breakpoint
                            console.log('Desktop mode - showing sidebar');
                            sidebar.classList.remove('-translate-x-full');
                            sidebar.classList.add('translate-x-0');
                            sidebar.style.display = 'block';
                            sidebar.style.visibility = 'visible';

                            // Ensure main content has proper margin
                            mainContent.classList.add('lg:ms-64');
                            mainContent.classList.remove('lg:ms-0');
                        } else {
                            console.log('Mobile mode - hiding sidebar');
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');

                            // Remove margin on mobile
                            mainContent.classList.remove('lg:ms-64', 'lg:ms-0');
                        }
                    }

                    // Initial setup
                    ensureDesktopSidebar();

                    // Handle window resize
                    window.addEventListener('resize', ensureDesktopSidebar);

                    // Toggle functionality
                    if (toggleButton) {
                        toggleButton.addEventListener('click', function(e) {
                            console.log('Toggle button clicked');
                            e.preventDefault();

                            if (window.innerWidth >= 1024) {
                                // Desktop toggle
                                sidebar.classList.toggle('-translate-x-full');
                                sidebar.classList.toggle('translate-x-0');

                                if (sidebar.classList.contains('-translate-x-full')) {
                                    // Sidebar is hidden
                                    mainContent.classList.remove('lg:ms-64');
                                    mainContent.classList.add('lg:ms-0');
                                    console.log('Sidebar hidden on desktop');
                                } else {
                                    // Sidebar is visible
                                    mainContent.classList.remove('lg:ms-0');
                                    mainContent.classList.add('lg:ms-64');
                                    console.log('Sidebar shown on desktop');
                                }
                            }
                        });
                    }
                } else {
                    console.log('Some sidebar elements not found');
                    if (!sidebar) console.log('Sidebar element not found');
                    if (!toggleButton) console.log('Toggle button not found');
                    if (!mainContent) console.log('Main content not found');
                }
            }, 100); // Small delay to ensure DOM is fully ready
        });
    </script>
</body>
</html>
