---
title: Tailwind CSS Products Page - Flowbite
description: Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more.
layout: dashboard
footer: true
group: e-commerce
page: products
---

<div class="grid grid-cols-12 bg-white dark:bg-gray-900">
  <div class="col-span-full mx-4 mt-4 ">
    <nav class="mb-4 flex" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
        <li class="inline-flex items-center">
          <a href="#" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-700 dark:text-gray-400 dark:hover:text-white">
            <svg class="me-2.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M11.3 3.3a1 1 0 0 1 1.4 0l6 6 2 2a1 1 0 0 1-1.4 1.4l-.3-.3V19a2 2 0 0 1-2 2h-3a1 1 0 0 1-1-1v-3h-2v3c0 .6-.4 1-1 1H7a2 2 0 0 1-2-2v-6.6l-.3.3a1 1 0 0 1-1.4-1.4l2-2 6-6Z" clip-rule="evenodd" />
            </svg>
            Home
          </a>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="mx-1 h-4 w-4 text-gray-400 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7" />
            </svg>
            <a href="#" class="ms-1 text-sm font-medium text-gray-700 hover:text-primary-700 dark:text-gray-400 dark:hover:text-white md:ms-2">E-commerce</a>
          </div>
        </li>
        <li aria-current="page">
          <div class="flex items-center">
            <svg class="mx-1 h-4 w-4 text-gray-400 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7" />
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 dark:text-gray-400 md:ms-2">All products</span>
          </div>
        </li>
      </ol>
    </nav>
    <h1 class="mb-4 text-xl font-semibold text-gray-900 dark:text-white">All products</h1>
    <div class="grid grid-cols-2 gap-x-4 gap-y-4 lg:grid-cols-4 lg:gap-y-0">
      <div class="space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
        <span class="inline-flex items-center rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">
          <svg class="-ms-1 me-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v13m0-13 4 4m-4-4-4 4" />
          </svg>
          8%
        </span>
        <h2 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">345,768</h2>
        <p class="text-gray-500 dark:text-gray-400">Total stocks</p>
      </div>
      <div class="space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
        <span class="inline-flex items-center rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">
          <svg class="-ms-1 me-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v13m0-13 4 4m-4-4-4 4" />
          </svg>
          12,4
        </span>
        <h2 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">31,385</h2>
        <p class="text-gray-500 dark:text-gray-400">New products</p>
      </div>
      <div class="space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
        <span class="inline-flex items-center rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">
          <svg class="-ms-1 me-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19V5m0 14-4-4m4 4 4-4" />
          </svg>
          3,7
        </span>
        <h2 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">27,274</h2>
        <p class="text-gray-500 dark:text-gray-400">Sold products</p>
      </div>
      <div class="space-y-3 rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800">
        <span class="inline-flex items-center rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">
          <svg class="-ms-1 me-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19V5m0 14-4-4m4 4 4-4" />
          </svg>
          12,4
        </span>
        <h2 class="text-2xl font-bold leading-none text-gray-900 dark:text-white">3,506</h2>
        <p class="text-gray-500 dark:text-gray-400">Returned products</p>
      </div>
    </div>
  </div>
  <!-- Table -->
  <div class="relative col-span-12 overflow-hidden bg-white dark:bg-gray-900">
    <div class="items-center justify-between space-y-3 border-b p-4 dark:border-gray-800 border-gray-200 sm:flex sm:space-x-4 sm:space-y-0 md:flex-row">
      <div class="flex items-center space-x-1.5">
        <h5 class="font-semibold dark:text-white">Flowbite Products</h5>
        <button type="button" data-tooltip-target="results-tooltip" class="text-gray-400 hover:text-gray-900 dark:hover:text-white">
          <svg class="h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm9.4-5.5a1 1 0 1 0 0 2 1 1 0 1 0 0-2ZM10 10a1 1 0 1 0 0 2h1v3h-1a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-1v-4c0-.6-.4-1-1-1h-2Z" clip-rule="evenodd" />
          </svg>
          <span class="sr-only">More info</span>
        </button>
        <div id="results-tooltip" role="tooltip" class="tooltip invisible absolute z-10 inline-block rounded-lg bg-gray-900 px-3 py-2 text-sm font-medium text-white opacity-0 shadow-xs transition-opacity duration-300 dark:bg-gray-700">
          Showing 1-10 of 6,560 results
          <div class="tooltip-arrow" data-popper-arrow=""></div>
        </div>
      </div>
      <div class="flex items-center space-x-3 sm:justify-end">
        <button
          id="createProductButton"
          data-modal-target="createProductModal"
          data-modal-toggle="createProductModal"
          type="button"
          class="flex w-full items-center justify-center rounded-lg bg-primary-700 px-3 py-2 text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 sm:w-auto"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5" />
          </svg>
          Add new product
        </button>
        <button
          id="settingsDropdownButton"
          data-dropdown-toggle="settingsDropdown"
          type="button"
          class="flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700 sm:w-auto"
        >
          Table settings
          <svg class="-me-0.5 ms-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
          </svg>
        </button>
        <!-- Table settings dropdown -->
        <div id="settingsDropdown" class="z-10 hidden w-56 rounded-lg bg-white p-3 shadow-sm dark:bg-gray-700" aria-labelledby="settingsDropdownButton">
          <div class="mb-4">
            <h6 class="mb-2 text-sm font-semibold text-gray-900 dark:text-white">Columns</h6>
            <ul class="space-y-2 text-sm" aria-labelledby="dropdownFilter">
              <li>
                <label for="category-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="category-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Category
                </label>
              </li>

              <li>
                <label for="brand-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="brand-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Brand
                </label>
              </li>

              <li>
                <label for="price-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="price-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Price
                </label>
              </li>

              <li>
                <label for="stock-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="stock-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Stock
                </label>
              </li>

              <li>
                <label for="sales-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="sales-checkbox"
                    type="checkbox"
                    value=""
                    checked
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Total sales
                </label>
              </li>

              <li>
                <label for="status-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="status-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Status
                </label>
              </li>
            </ul>
          </div>
          <div class="mb-4">
            <h6 class="mb-2 text-sm font-semibold text-gray-900 dark:text-white">Additional settings</h6>
            <ul class="space-y-2 text-sm">
              <li>
                <label for="description-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="description-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Show description
                </label>
              </li>
              <li>
                <label for="details-checkbox" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="details-checkbox"
                    type="checkbox"
                    value=""
                    class="mr-2 h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-700 dark:focus:ring-primary-600"
                  />
                  Show advanced details
                </label>
              </li>
            </ul>
          </div>
          <div class="mb-4">
            <h6 class="mb-2 text-sm font-semibold text-gray-900 dark:text-white">Row height</h6>
            <ul class="space-y-2 text-sm" aria-labelledby="dropdownFilter">
              <li>
                <label for="normal-radio" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="normal-radio"
                    type="radio"
                    value=""
                    name="assigned-radio"
                    class="me-2 h-4 w-4 border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                  />
                  Normal
                </label>
              </li>

              <li>
                <label for="compact-radio" class="flex w-full items-center rounded-md px-1.5 py-1 text-sm font-medium text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600">
                  <input
                    id="compact-radio"
                    type="radio"
                    value=""
                    name="assigned-radio"
                    class="me-2 h-4 w-4 border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-500 dark:bg-gray-600 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                  />
                  Compact
                </label>
              </li>
            </ul>
          </div>
          <button
            type="button"
            class="w-full rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
          >
            Apply to all products
          </button>
        </div>
      </div>
    </div>
    <div class="flex flex-col-reverse items-start justify-between p-4 md:flex-row md:items-center md:space-x-4">
      <div class="grid w-full grid-cols-2 gap-4 md:grid-cols-4 lg:w-2/3 mb-4 mb:mb-0">
        <div class="relative z-0 w-full">
          <input
            type="text"
            id="floating_standard"
            class="peer block w-full appearance-none border-0 border-b-2 border-gray-200 bg-transparent px-0 py-2.5 text-sm text-gray-900 focus:border-primary-600 focus:outline-none focus:ring-0 dark:border-gray-700 dark:text-white dark:focus:border-primary-500"
            placeholder=" "
          />
          <label
            for="floating_standard"
            class="absolute top-3 -z-10 origin-[0] -translate-y-6 scale-75 text-sm text-gray-500 duration-300 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:scale-100 peer-focus:start-0 peer-focus:-translate-y-6 peer-focus:scale-75 peer-focus:text-primary-600 dark:text-gray-400 peer-focus:dark:text-primary-500 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4"
          >
            Search
          </label>
        </div>
        <div class="w-full">
          <label for="brand" class="sr-only">Brand</label>
          <select
            id="brand"
            class="peer block w-full appearance-none border-0 border-b-2 border-gray-200 bg-transparent px-0 py-2.5 text-sm text-gray-500 focus:border-gray-200 focus:outline-none focus:ring-0 dark:border-gray-700 dark:text-gray-400"
          >
            <option selected="">Brand</option>
            <option value="purple">Samsung</option>
            <option value="primary">Apple</option>
            <option value="pink">Pink</option>
            <option value="green">Green</option>
          </select>
        </div>
        <div class="w-full">
          <label for="price" class="sr-only">Price</label>
          <select
            id="price"
            class="peer block w-full appearance-none border-0 border-b-2 border-gray-200 bg-transparent px-0 py-2.5 text-sm text-gray-500 focus:border-gray-200 focus:outline-none focus:ring-0 dark:border-gray-700 dark:text-gray-400"
          >
            <option selected="">Price</option>
            <option value="below-100">$ 1-100</option>
            <option value="below-500">$ 101-500</option>
            <option value="below-1000">$ 501-1000</option>
            <option value="over-1000">$ 1001+</option>
          </select>
        </div>
        <div class="w-full">
          <label for="category" class="sr-only">Category</label>
          <select
            id="category"
            class="peer block w-full appearance-none border-0 border-b-2 border-gray-200 bg-transparent px-0 py-2.5 text-sm text-gray-500 focus:border-gray-200 focus:outline-none focus:ring-0 dark:border-gray-700 dark:text-gray-400"
          >
            <option selected="">Category</option>
            <option value="pc">PC</option>
            <option value="phone">Phone</option>
            <option value="tablet">Tablet</option>
            <option value="console">Gaming/Console</option>
          </select>
        </div>
      </div>
      <div class="mb-3 md:mb-0 w-full md:w-auto">
        <button
          id="actionsDropdownButton"
          data-dropdown-toggle="actionsDropdown"
          class="flex w-full items-center justify-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700 md:w-auto"
          type="button"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
          </svg>
          Actions
        </button>
        <div id="actionsDropdown" class="z-10 hidden w-40 rounded-lg bg-white shadow-sm dark:bg-gray-700">
          <ul class="p-2 text-sm font-medium text-gray-500 dark:text-gray-400" aria-labelledby="actionsDropdownButton">
            <li>
              <a
                id="editProductButton"
                data-modal-target="editProductModal"
                data-modal-toggle="editProductModal"
                href="#editProductModal"
                class="inline-flex w-full items-center rounded-md px-3 py-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
              >
                <svg class="me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                  <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                  <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                </svg>
                Edit
              </a>
            </li>
            <li>
              <a
                id="archiveButton"
                data-modal-target="archiveModal"
                data-modal-toggle="archiveModal"
                href="#archiveModal"
                class="inline-flex w-full items-center rounded-md px-3 py-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
              >
                <svg class="me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    fill-rule="evenodd"
                    d="M5 7.8C6.7 6.3 9.2 5 12 5s5.3 1.3 7 2.8a12.7 12.7 0 0 1 2.7 3.2c.*******.3 1s-.1.8-.3 1a2 2 0 0 1-.6 1 12.7 12.7 0 0 1-9.1 5c-2.8 0-5.3-1.3-7-2.8A12.7 12.7 0 0 1 2.3 13c-.2-.2-.3-.6-.3-1s.1-.8.3-1c.1-.4.3-.7.6-1 .5-.7 1.2-1.5 2.1-2.2Zm7 7.2a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"
                    clip-rule="evenodd"
                  />
                </svg>
                Archive
              </a>
            </li>
            <li>
              <a
                id="deleteProductsButton"
                data-modal-target="deleteProductsModal"
                data-modal-toggle="deleteProductsModal"
                href="#deleteProductsModal"
                class="inline-flex w-full items-center rounded-md px-3 py-2 text-red-600 hover:bg-gray-100 dark:text-red-500 dark:hover:bg-gray-600"
              >
                <svg class="me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                  <path
                    fill-rule="evenodd"
                    d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                    clip-rule="evenodd"
                  />
                </svg>
                Delete
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="overflow-x-auto">
      <table class="w-full text-left text-sm text-gray-500 dark:text-gray-400">
        <thead class="bg-gray-50 text-xs uppercase dark:bg-gray-800">
          <tr>
            <th scope="col" class="p-4">
              <div class="flex items-center">
                <input
                  id="checkbox-all"
                  type="checkbox"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-all" class="sr-only">checkbox</label>
              </div>
            </th>
            <th scope="col" class="px-4 py-3">
              <span class="sr-only">Expand/Collapse Row</span>
            </th>
            <th scope="col" class="min-w-56 px-4 py-3">Product</th>
            <th scope="col" class="min-w-40 px-4 py-3">
              <div class="inline-flex items-center">
                Category
                <svg class="ml-1 inline-block h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 15 4 4 4-4m0-6-4-4-4 4" />
                </svg>
              </div>
            </th>
            <th scope="col" class="min-w-24 px-4 py-3">
              <div class="inline-flex items-center">
                Brand
                <svg class="ml-1 inline-block h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 15 4 4 4-4m0-6-4-4-4 4" />
                </svg>
              </div>
            </th>
            <th scope="col" class="min-w-24 px-4 py-3">
              <div class="inline-flex items-center">
                Price
                <svg class="ml-1 inline-block h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 15 4 4 4-4m0-6-4-4-4 4" />
                </svg>
              </div>
            </th>
            <th scope="col" class="min-w-24 px-4 py-3">
              <div class="inline-flex items-center">
                Stock
                <svg class="ml-1 inline-block h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 15 4 4 4-4m0-6-4-4-4 4" />
                </svg>
              </div>
            </th>
            <th scope="col" class="min-w-48 px-4 py-3">
              <div class="inline-flex items-center">
                Total Sales
                <svg class="ml-1 inline-block h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m8 15 4 4 4-4m0-6-4-4-4 4" />
                </svg>
              </div>
            </th>
            <th scope="col" class="min-w-28 px-4 py-3">Status</th>
          </tr>
        </thead>
        <tbody data-accordion="table-column">
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-0"
            data-accordion-target="#table-column-body-0"
            aria-expanded="false"
            aria-controls="table-column-body-0"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/imac-front.svg" alt="imac image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/imac-front-dark.svg" alt="imac image" />
              </div>
              Apple iMac 27&#34;
            </th>
            <td class="px-4 py-3">PC</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Apple</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$2999</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">200</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">245</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-0" aria-labelledby="table-column-header-0">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-front.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-front-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-components.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-components-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-back.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-back-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>
                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-1"
            data-accordion-target="#table-column-body-1"
            aria-expanded="false"
            aria-controls="table-column-body-1"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/macbook-pro-light.svg" alt="macbook image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/macbook-pro-dark.svg" alt="macbook image" />
              </div>
              Apple MacBook PRO
            </th>
            <td class="px-4 py-3">PC</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Apple</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$1499</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">1237</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">2000</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-1" aria-labelledby="table-column-header-1">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/macbook-pro-light.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/macbook-pro-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-keyboard.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-keyboard-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/airpods.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/airpods-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/macbook-pro-light.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/macbook-pro-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Retina displays with True Tone technology, up to 8TB of SSD storage, and up to 96GB of unified memory. With a sleek aluminum design, advanced thermal systems, and up to 22 hours of battery life, it’s ideal for demanding
                  tasks like video editing, software development, and graphic design.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>
                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-gray-600"></div>
                    <div class="h-6 w-6 rounded-full bg-gray-400"></div>
                    <div class="h-6 w-6 rounded-full bg-gray-800"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">3kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-2"
            data-accordion-target="#table-column-body-2"
            aria-expanded="false"
            aria-controls="table-column-body-2"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/iphone-light.svg" alt="imac image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/iphone-dark.svg" alt="imac image" />
              </div>
              Apple iPhone 14
            </th>
            <td class="px-4 py-3">Phone</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Apple</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$999</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">300</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">466</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">Inactive</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-2" aria-labelledby="table-column-header-2">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/iphone-light.svg" alt="iPhone Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/iphone-dark.svg" alt="iPhone Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/iphone-camera.svg" alt="iPhone Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/iphone-camera-dark.svg" alt="iPhone Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/airpods.svg" alt="iPhone Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/airpods-dark.svg" alt="iPhone Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/iphone-light.svg" alt="iPhone Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/iphone-dark.svg" alt="iPhone Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-3"
            data-accordion-target="#table-column-body-3"
            aria-expanded="false"
            aria-controls="table-column-body-3"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/ipad-light.svg" alt="imac image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/ipad-dark.svg" alt="imac image" />
              </div>
              Apple iPad Air
            </th>
            <td class="px-4 py-3">Tablet</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Apple</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$1199</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">4576</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">90</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-3" aria-labelledby="table-column-header-3">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-light.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-keyboard.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-keyboard-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-4"
            data-accordion-target="#table-column-body-4"
            aria-expanded="false"
            aria-controls="table-column-body-4"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/xbox-side.svg" alt="imac image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/xbox-side-dark.svg" alt="imac image" />
              </div>
              Xbox Series S
            </th>
            <td class="px-4 py-3">Gaming/Console</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Microsoft</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$299</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">56</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">3087</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-4" aria-labelledby="table-column-header-4">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-side.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-side-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-controller.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-controller-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-light.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-side.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-side-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-5"
            data-accordion-target="#table-column-body-5"
            aria-expanded="false"
            aria-controls="table-column-body-5"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/ps5-light.svg" alt="ps5 image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/ps5-dark.svg" alt="ps5 image" />
              </div>
              PlayStation 5
            </th>
            <td class="px-4 py-3">Gaming/Console</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Sony</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$799</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">78</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">2999</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">Inactive</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-5" aria-labelledby="table-column-header-5">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ps5-light.svg" alt="ps5 Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ps5-dark.svg" alt="ps5 Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ps5-side.svg" alt="ps5 Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ps5-side-dark.svg" alt="ps5 Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ps5-controller.svg" alt="ps5 Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ps5-controller-dark.svg" alt="ps5 Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ps5-light.svg" alt="ps5 Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ps5-dark.svg" alt="ps5 Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  AMD Ryzen Zen 2 CPU with 8 cores running at 3.5 GHz and an AMD RDNA 2 GPU delivering 10.28 TFLOPS for stunning 4K visuals with ray tracing. It includes 16GB of GDDR6 RAM and an ultra-fast 825GB SSD for near-instant load
                  times, expandable via NVMe.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-gray-600"></div>
                    <div class="h-6 w-6 rounded-full bg-white"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Sony</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">8kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-6"
            data-accordion-target="#table-column-body-6"
            aria-expanded="false"
            aria-controls="table-column-body-6"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/xbox-light.svg" alt="xbox image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/xbox-dark.svg" alt="xbox image" />
              </div>
              Xbox Series X
            </th>
            <td class="px-4 py-3">Gaming/Console</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Microsoft</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$699</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">200</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">1870</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-6" aria-labelledby="table-column-header-6">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-side.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-side-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-controller.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-controller-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-light.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/xbox-side.svg" alt="xbox Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/xbox-side-dark.svg" alt="xbox Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-7"
            data-accordion-target="#table-column-body-7"
            aria-expanded="false"
            aria-controls="table-column-body-7"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/apple-watch-light.svg" alt="watch image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/apple-watch-dark.svg" alt="watch image" />
              </div>
              Apple Watch SE
            </th>
            <td class="px-4 py-3">Watch</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Apple</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$399</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">657</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">5067</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-7" aria-labelledby="table-column-header-7">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/apple-watch-light.svg" alt="watch Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/apple-watch-dark.svg" alt="watch Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/airpods.svg" alt="ps5 Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/airpods-dark.svg" alt="ps5 Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/apple-watch-light.svg" alt="watch Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/apple-watch-dark.svg" alt="watch Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/apple-watch-light.svg" alt="watch Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/apple-watch-dark.svg" alt="watch Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>

                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-8"
            data-accordion-target="#table-column-body-8"
            aria-expanded="false"
            aria-controls="table-column-body-8"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/ipad-keyboard.svg" alt="tablet image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/ipad-keyboard-dark.svg" alt="tablet image" />
              </div>
              iPad Pro 13-Inch (M4)
            </th>
            <td class="px-4 py-3">Photo</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">Nikon</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$599</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">465</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">1870</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800 dark:bg-red-900 dark:text-red-300">Inactive</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-8" aria-labelledby="table-column-header-8">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-light.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-keyboard.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-keyboard-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/ipad-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/ipad-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>
                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>
                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
          <tr
            class="cursor-pointer border-b transition hover:bg-gray-100 dark:border-gray-800 dark:hover:bg-gray-800 border-gray-200"
            id="table-column-header-9"
            data-accordion-target="#table-column-body-9"
            aria-expanded="false"
            aria-controls="table-column-body-9"
          >
            <td class="w-4 px-4 py-3">
              <div class="flex items-center">
                <input
                  id="checkbox-table-search-1"
                  type="checkbox"
                  onclick="event.stopPropagation()"
                  class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                />
                <label for="checkbox-table-search-1" class="sr-only">checkbox</label>
              </div>
            </td>
            <td class="w-4 p-3">
              <svg data-accordion-icon="" class="h-5 w-5 shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7" />
              </svg>
            </td>
            <th scope="row" class="flex items-center whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">
              <div class="me-3 aspect-square h-8 w-8 shrink-0">
                <img class="h-full w-full dark:hidden" src="/images/e-commerce/imac-front.svg" alt="imac image" />
                <img class="hidden h-full w-full dark:block" src="/images/e-commerce/imac-front-dark.svg" alt="imac image" />
              </div>
              Apple iMac 20&#34;
            </th>
            <td class="px-4 py-3">TV/Monitor</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">BenQ</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">$499</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">354</td>
            <td class="whitespace-nowrap px-4 py-3 font-medium text-gray-900 dark:text-white">76</td>
            <td class="whitespace-nowrap px-4 py-3">
              <div class="me-2 mr-2 w-fit rounded-sm bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:bg-green-900 dark:text-green-300">Active</div>
            </td>
          </tr>
          <tr class="hidden w-full flex-1 overflow-x-auto" id="table-column-body-9" aria-labelledby="table-column-header-9">
            <td class="border-b p-4 dark:border-gray-800 border-gray-200" colspan="9">
              <div class="mb-4 grid grid-cols-4 gap-4">
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-front.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-front-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-components.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-components-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
                <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
                  <img src="/images/e-commerce/imac-back.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
                  <img src="/images/e-commerce/imac-back-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
                </div>
              </div>
              <div>
                <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Details</h6>
                <div class="max-w-screen-md text-base text-gray-500 dark:text-gray-400">
                  Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2,
                  Magic Keyboard - US.
                </div>
              </div>
              <div class="mt-4 grid grid-cols-4 gap-4">
                <div class="relative flex flex-col items-start justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Product State</h6>
                  <div class="flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-900 dark:text-primary-300">
                    <svg class="mr-1 h-3 w-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                      <path
                        d="M13.849 4.22c-.684-1.626-3.014-1.626-3.698 0L8.397 8.387l-4.552.361c-1.775.14-2.495 2.331-1.142 3.477l3.468 2.937-1.06 4.392c-.413 1.713 1.472 3.067 2.992 2.149L12 19.35l3.897 2.354c1.52.918 3.405-.436 2.992-2.15l-1.06-4.39 3.468-2.938c1.353-1.146.633-3.336-1.142-3.477l-4.552-.36-1.754-4.17Z"
                      />
                    </svg>
                    New
                  </div>
                </div>
                <div class="relative flex flex-col justify-between rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Shipping</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">
                    <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                      <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
                    </svg>

                    Worldwide
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Colors</h6>
                  <div class="flex items-center space-x-2">
                    <div class="h-6 w-6 rounded-full bg-purple-600"></div>
                    <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
                    <div class="h-6 w-6 rounded-full bg-primary-600"></div>
                    <div class="h-6 w-6 rounded-full bg-pink-400"></div>
                    <div class="h-6 w-6 rounded-full bg-teal-300"></div>
                    <div class="h-6 w-6 rounded-full bg-green-300"></div>
                  </div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Brand</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Apple</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Sold by</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Ships from</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">Flowbite</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Dimensions (cm)</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">105 x 15 x 23</div>
                </div>
                <div class="relative rounded-lg bg-gray-100 p-3 dark:bg-gray-800">
                  <h6 class="mb-2 text-base font-medium leading-none text-gray-900 dark:text-white">Item weight</h6>
                  <div class="flex items-center text-gray-500 dark:text-gray-400">12kg</div>
                </div>
              </div>
              <div class="mt-4 flex items-center space-x-3">
                <button
                  id="editProductButton"
                  data-modal-target="editProductModal"
                  data-modal-toggle="editProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path fill-rule="evenodd" d="M11.3 6.2H5a2 2 0 0 0-2 2V19a2 2 0 0 0 2 2h11c1.1 0 2-1 2-2.1V11l-4 4.2c-.3.3-.7.6-1.2.7l-2.7.6c-1.7.3-3.3-1.3-3-3.1l.6-2.9c.1-.5.4-1 .7-1.3l3-3.1Z" clip-rule="evenodd" />
                    <path fill-rule="evenodd" d="M19.8 4.3a2.1 2.1 0 0 0-1-1.1 2 2 0 0 0-2.2.4l-.6.6 2.9 3 .5-.6a2.1 2.1 0 0 0 .6-1.5c0-.2 0-.5-.2-.8Zm-2.4 4.4-2.8-3-4.8 5-.1.3-.7 3c0 .*******.6l2.7-.6.3-.1 4.7-5Z" clip-rule="evenodd" />
                  </svg>
                  Edit
                </button>
                <button
                  id="readProductButton"
                  data-modal-target="readProductModal"
                  data-modal-toggle="readProductModal"
                  type="button"
                  class="flex items-center rounded-lg border border-gray-200 bg-white px-3 py-2 text-center text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
                >
                  Preview
                </button>
                <button
                  id="deleteProductButton"
                  data-modal-target="deleteProductModal"
                  data-modal-toggle="deleteProductModal"
                  type="button"
                  class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
                >
                  <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      fill-rule="evenodd"
                      d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  Delete
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="items-start justify-between space-y-4 px-4 pb-4 pt-3 dark:bg-gray-800 sm:flex sm:space-y-0 md:flex-row md:items-center" aria-label="Table navigation">
      <div class="flex items-center space-x-5 text-xs">
        <div>
          <div class="mb-1 text-gray-500 dark:text-gray-400">Purchase price</div>
          <div class="font-medium dark:text-white">$ 3,567,890</div>
        </div>
        <div>
          <div class="mb-1 text-gray-500 dark:text-gray-400">Total selling price</div>
          <div class="font-medium dark:text-white">$ 8,489,400</div>
        </div>
      </div>
      <div class="flex items-center space-x-4">
        <button
          type="button"
          class="flex items-center rounded-lg py-1.5 text-center text-sm font-medium text-primary-700 hover:text-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:text-primary-500 dark:hover:text-primary-600 dark:focus:ring-primary-800"
        >
          Print barcodes
        </button>
        <button
          type="button"
          class="flex items-center rounded-lg py-1.5 text-center text-sm font-medium text-primary-700 hover:text-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:text-primary-500 dark:hover:text-primary-600 dark:focus:ring-primary-800"
        >
          Duplicate
        </button>
        <button
          type="button"
          class="flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-xs font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
        >
          Export CSV
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Create product modal -->
<div id="createProductModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-3xl p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-5">
      <!-- Modal header -->
      <div class="mb-4 flex items-center justify-between rounded-t sm:mb-5">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Add Product</h3>
        <button
          type="button"
          class="ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
          data-modal-toggle="createProductModal"
        >
          <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
          </svg>
          <span class="sr-only">Close modal</span>
        </button>
      </div>
      <!-- Modal body -->
      <form action="#">
        <div class="mb-4 grid gap-4 sm:grid-cols-2">
          <div>
            <label for="name" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Product Name</label>
            <input
              type="text"
              name="name"
              id="name"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Type product name"
              required=""
            />
          </div>
          <div>
            <label for="category" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Category</label>
            <select
              id="category"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
            >
              <option selected="">Select category</option>
              <option value="TV">TV/Monitors</option>
              <option value="PC">PC</option>
              <option value="GA">Gaming/Console</option>
              <option value="PH">Phones</option>
            </select>
          </div>
          <div>
            <label for="brand" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Brand</label>
            <input
              type="text"
              name="brand"
              id="brand"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Product brand"
              required=""
            />
          </div>
          <div>
            <label for="price" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Price</label>
            <input
              type="number"
              name="price"
              id="price"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="$2999"
              required=""
            />
          </div>
          <div class="grid gap-4 sm:col-span-2 sm:grid-cols-4 md:gap-6">
            <div>
              <label for="weight" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Item weight (kg)</label>
              <input
                type="number"
                name="weight"
                id="weight"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="12"
                required=""
              />
            </div>
            <div>
              <label for="length" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Lenght (cm)</label>
              <input
                type="number"
                name="length"
                id="length"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="105"
                required=""
              />
            </div>
            <div>
              <label for="breadth" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Breadth (cm)</label>
              <input
                type="number"
                name="breadth"
                id="breadth"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="15"
                required=""
              />
            </div>
            <div>
              <label for="width" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Width (cm)</label>
              <input
                type="number"
                name="width"
                id="width"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="23"
                required=""
              />
            </div>
          </div>
          <div class="sm:col-span-2">
            <label for="description" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Description</label>
            <textarea
              id="description"
              rows="4"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Write product description here"
            ></textarea>
          </div>
        </div>
        <div class="mb-4 space-y-4 sm:flex sm:space-y-0">
          <div class="mr-4 flex items-center">
            <input
              id="inline-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">In-store only</label>
          </div>
          <div class="mr-4 flex items-center">
            <input
              id="inline-2-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-2-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Online selling only</label>
          </div>
          <div class="mr-4 flex items-center">
            <input
              checked=""
              id="inline-checked-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-checked-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Both in-store and online</label>
          </div>
        </div>
        <div class="mb-4">
          <span class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Product Images</span>
          <div class="flex w-full items-center justify-center">
            <label
              for="dropzone-file"
              class="flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600"
            >
              <div class="flex flex-col items-center justify-center pb-6 pt-5">
                <svg aria-hidden="true" class="mb-3 h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
              </div>
              <input id="dropzone-file" type="file" class="hidden" />
            </label>
          </div>
        </div>
        <div class="items-center space-y-4 sm:flex sm:space-x-4 sm:space-y-0">
          <button
            type="submit"
            class="inline-flex w-full justify-center rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 sm:w-auto"
          >
            Add product
          </button>
          <button
            class="inline-flex w-full items-center justify-center rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 sm:w-auto"
          >
            <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path
                fill-rule="evenodd"
                d="M12.512 8.72a2.46 2.46 0 0 1 3.479 0 2.461 2.461 0 0 1 0 3.479l-.004.005-1.094 1.08a.998.998 0 0 0-.194-.272l-3-3a1 1 0 0 0-.272-.193l1.085-1.1Zm-2.415 2.445L7.28 14.017a1 1 0 0 0-.289.702v2a1 1 0 0 0 1 1h2a1 1 0 0 0 .703-.288l2.851-2.816a.995.995 0 0 1-.26-.189l-3-3a.998.998 0 0 1-.19-.26Z"
                clip-rule="evenodd"
              />
              <path
                fill-rule="evenodd"
                d="M7 3a1 1 0 0 1 1 1v1h3V4a1 1 0 1 1 2 0v1h3V4a1 1 0 1 1 2 0v1h1a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V7a2 2 0 0 1 2-2h1V4a1 1 0 0 1 1-1Zm10.67 8H19v8H5v-8h3.855l.53-.537a1 1 0 0 1 .87-.285c.**************.277.087.045-.043-.073-.18-.09-.276a1 1 0 0 1 .274-.873l1.09-1.104a3.46 3.46 0 0 1 4.892 0l.001.002A3.461 3.461 0 0 1 17.67 11Z"
                clip-rule="evenodd"
              />
            </svg>

            Schedule
          </button>
          <button
            data-modal-toggle="createProductModal"
            type="button"
            class="w-full rounded-lg border border-gray-200 bg-white px-5 py-2.5 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700 sm:w-auto"
          >
            Discard
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Edit product modal -->
<div id="editProductModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-3xl p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-5">
      <!-- Modal header -->
      <div class="mb-4 flex items-center justify-between rounded-t sm:mb-5">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Update Product</h3>
        <button
          type="button"
          class="ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
          data-modal-toggle="editProductModal"
        >
          <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
          </svg>
          <span class="sr-only">Close modal</span>
        </button>
      </div>
      <!-- Modal body -->
      <form action="#">
        <div class="mb-4 grid gap-4 sm:grid-cols-2">
          <div>
            <label for="name" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Product Name</label>
            <input
              type="text"
              name="name"
              id="name"
              value="Apple iPad 5th Gen Wi-Fi"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Ex. Apple iMac 27&ldquo;"
              required=""
            />
          </div>
          <div>
            <label for="category" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Category</label>
            <select
              id="category"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
            >
              <option selected="">Electronics</option>
              <option value="TV">TV/Monitors</option>
              <option value="PC">PC</option>
              <option value="GA">Gaming/Console</option>
              <option value="PH">Phones</option>
            </select>
          </div>
          <div>
            <label for="brand" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Brand</label>
            <input
              type="text"
              name="brand"
              id="brand"
              value="Tesla"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Ex. Apple"
              required=""
            />
          </div>
          <div>
            <label for="price" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Price</label>
            <input
              type="number"
              name="price"
              id="price"
              value="259"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="$2999"
              required=""
            />
          </div>
          <div class="grid gap-4 sm:col-span-2 sm:grid-cols-4 md:gap-6">
            <div>
              <label for="weight" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Item weight (kg)</label>
              <input
                type="number"
                name="weight"
                id="weight"
                value="32"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="Ex. 12"
                required=""
              />
            </div>
            <div>
              <label for="length" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Lenght (cm)</label>
              <input
                type="number"
                name="length"
                id="length"
                value="126"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="Ex. 105"
                required=""
              />
            </div>
            <div>
              <label for="breadth" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Breadth (cm)</label>
              <input
                type="number"
                name="breadth"
                id="breadth"
                value="121"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="Ex. 15"
                required=""
              />
            </div>
            <div>
              <label for="width" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Width (cm)</label>
              <input
                type="number"
                name="width"
                id="width"
                value="953"
                class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
                placeholder="Ex. 23"
                required=""
              />
            </div>
          </div>
          <div class="sm:col-span-2">
            <label for="description" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Description</label>
            <textarea
              id="description"
              rows="4"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="Write your description..."
            >
Standard glass, 3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2, Magic Keyboard - US
            </textarea>
          </div>
        </div>
        <div class="mb-4 space-y-4 sm:flex sm:space-y-0">
          <div class="mr-4 flex items-center">
            <input
              id="inline-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">In-store only</label>
          </div>
          <div class="mr-4 flex items-center">
            <input
              id="inline-2-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-2-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Online selling only</label>
          </div>
          <div class="mr-4 flex items-center">
            <input
              checked=""
              id="inline-checked-checkbox"
              type="checkbox"
              value=""
              name="sellingType"
              class="h-4 w-4 rounded-sm border-gray-300 bg-gray-100 text-primary-600 focus:ring-2 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
            />
            <label for="inline-checked-checkbox" class="ml-2 text-sm font-medium text-gray-900 dark:text-gray-300">Both in-store and online</label>
          </div>
        </div>
        <div class="mb-4">
          <span class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Product Images</span>
          <div class="mb-4 grid grid-cols-3 gap-4 sm:grid-cols-4">
            <div class="relative rounded-lg bg-gray-100 p-2 dark:bg-gray-700 sm:h-36 sm:w-36">
              <img src="/images/e-commerce/imac-components.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
              <img src="/images/e-commerce/imac-components-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
              <button type="button" class="absolute bottom-1 left-1 text-red-600 hover:text-red-500 dark:text-red-500 dark:hover:text-red-400">
                <svg aria-hidden="true" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="sr-only">Delete image</span>
              </button>
            </div>
            <div class="relative rounded-lg bg-gray-100 p-2 dark:bg-gray-700 sm:h-36 sm:w-36">
              <img src="/images/e-commerce/imac-front.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
              <img src="/images/e-commerce/imac-front-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
              <button type="button" class="absolute bottom-1 left-1 text-red-600 hover:text-red-500 dark:text-red-500 dark:hover:text-red-400">
                <svg aria-hidden="true" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="sr-only">Delete image</span>
              </button>
            </div>
            <div class="relative rounded-lg bg-gray-100 p-2 dark:bg-gray-700 sm:h-36 sm:w-36">
              <img src="/images/e-commerce/imac-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
              <img src="/images/e-commerce/imac-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
              <button type="button" class="absolute bottom-1 left-1 text-red-600 hover:text-red-500 dark:text-red-500 dark:hover:text-red-400">
                <svg aria-hidden="true" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="sr-only">Delete image</span>
              </button>
            </div>
            <div class="relative rounded-lg bg-gray-100 p-2 dark:bg-gray-700 sm:h-36 sm:w-36">
              <img src="/images/e-commerce/imac-back.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
              <img src="/images/e-commerce/imac-back-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
              <button type="button" class="absolute bottom-1 left-1 text-red-600 hover:text-red-500 dark:text-red-500 dark:hover:text-red-400">
                <svg aria-hidden="true" class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                    clip-rule="evenodd"
                  />
                </svg>
                <span class="sr-only">Delete image</span>
              </button>
            </div>
          </div>
          <div class="flex w-full items-center justify-center">
            <label
              for="dropzone-file"
              class="flex h-64 w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600"
            >
              <div class="flex flex-col items-center justify-center pb-6 pt-5">
                <svg aria-hidden="true" class="mb-3 h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">SVG, PNG, JPG or GIF (MAX. 800x400px)</p>
              </div>
              <input id="dropzone-file" type="file" class="hidden" />
            </label>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <button
            type="submit"
            class="rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
          >
            Update product
          </button>
          <button
            type="button"
            class="inline-flex items-center rounded-lg bg-red-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-red-800 focus:outline-none focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
          >
            <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path
                fill-rule="evenodd"
                d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
                clip-rule="evenodd"
              />
            </svg>
            Delete
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Preview product modal -->
<div id="readProductModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-3xl p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-5">
      <!-- Modal header -->
      <div class="mb-4 flex justify-between rounded-t sm:mb-5">
        <div class="text-lg text-gray-900 dark:text-white md:text-xl">
          <h3 class="font-semibold">Apple iMac 27”</h3>
          <p class="font-bold">$2999</p>
        </div>
        <div>
          <button type="button" class="inline-flex rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white" data-modal-toggle="readProductModal">
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
            </svg>
            <span class="sr-only">Close modal</span>
          </button>
        </div>
      </div>
      <div class="mb-4 grid grid-cols-3 gap-4 sm:mb-5 sm:grid-cols-4">
        <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
          <img src="/images/e-commerce/imac-front.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
          <img src="/images/e-commerce/imac-front-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
        </div>
        <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
          <img src="/images/e-commerce/imac-components.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
          <img src="/images/e-commerce/imac-components-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
        </div>
        <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
          <img src="/images/e-commerce/imac-side.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
          <img src="/images/e-commerce/imac-side-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
        </div>
        <div class="relative flex h-32 items-center justify-center rounded-lg bg-gray-100 p-2 dark:bg-gray-800 sm:h-36 sm:w-full">
          <img src="/images/e-commerce/imac-back.svg" alt="iMac Image" class="flex h-full w-auto dark:hidden" />
          <img src="/images/e-commerce/imac-back-dark.svg" alt="iMac Image" class="hidden h-full w-auto dark:flex" />
        </div>
      </div>
      <dl class="sm:mb-5">
        <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Details</dt>
        <dd class="mb-4 text-gray-500 dark:text-gray-400 sm:mb-5">
          Standard glass ,3.8GHz 8-core 10th-generation Intel Core i7 processor, Turbo Boost up to 5.0GHz, 16GB 2666MHz DDR4 memory, Radeon Pro 5500 XT with 8GB of GDDR6 memory, 256GB SSD storage, Gigabit Ethernet, Magic Mouse 2, Magic
          Keyboard - US.
        </dd>
        <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Colors</dt>
        <dd class="mb-4 flex items-center space-x-2 text-gray-500 dark:text-gray-400">
          <div class="h-6 w-6 rounded-full bg-purple-600"></div>
          <div class="h-6 w-6 rounded-full bg-indigo-400"></div>
          <div class="h-6 w-6 rounded-full bg-primary-600"></div>
          <div class="h-6 w-6 rounded-full bg-pink-400"></div>
          <div class="h-6 w-6 rounded-full bg-teal-300"></div>
          <div class="h-6 w-6 rounded-full bg-green-300"></div>
        </dd>
      </dl>
      <dl class="mb-4 grid grid-cols-2 gap-4 sm:mb-5 sm:grid-cols-3">
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Sold by</dt>
          <dd class="text-gray-500 dark:text-gray-400">Flowbite</dd>
        </div>
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Ships from</dt>
          <dd class="text-gray-500 dark:text-gray-400">Flowbite</dd>
        </div>
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Product State</dt>
          <dd class="text-gray-500 dark:text-gray-400">
            <span class="inline-flex items-center rounded-sm bg-primary-100 px-2.5 py-0.5 text-xs font-medium text-primary-800 dark:bg-primary-200 dark:text-primary-800">
              <svg aria-hidden="true" class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                />
              </svg>
              New
            </span>
          </dd>
        </div>
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Shipping</dt>
          <dd class="flex items-center text-gray-500 dark:text-gray-400">
            <svg class="mr-1 h-3.5 w-3.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M12 2a8 8 0 0 1 6.6 12.6l-.1.1-.6.7-5.1 6.2a1 1 0 0 1-1.6 0L6 15.3l-.3-.4-.2-.2v-.2A8 8 0 0 1 11.8 2Zm3 8a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" clip-rule="evenodd" />
            </svg>
            Worldwide
          </dd>
        </div>
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Dimensions (cm)</dt>
          <dd class="text-gray-500 dark:text-gray-400">105 x 15 x 23</dd>
        </div>
        <div class="rounded-lg bg-gray-100 p-3 dark:bg-gray-700">
          <dt class="mb-2 font-semibold leading-none text-gray-900 dark:text-white">Item weight</dt>
          <dd class="text-gray-500 dark:text-gray-400">12kg</dd>
        </div>
      </dl>
      <div class="flex items-center space-x-3 sm:space-x-4">
        <button
          type="button"
          class="inline-flex items-center rounded-lg bg-primary-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path
              fill-rule="evenodd"
              d="M11.32 6.176H5c-1.105 0-2 .949-2 2.118v10.588C3 20.052 3.895 21 5 21h11c1.105 0 2-.948 2-2.118v-7.75l-3.914 4.144A2.46 2.46 0 0 1 12.81 16l-2.681.568c-1.75.37-3.292-1.263-2.942-3.115l.536-2.839c.097-.512.335-.983.684-1.352l2.914-3.086Z"
              clip-rule="evenodd"
            />
            <path
              fill-rule="evenodd"
              d="M19.846 4.318a2.148 2.148 0 0 0-.437-.692 2.014 2.014 0 0 0-.654-.463 1.92 1.92 0 0 0-1.544 0 2.014 2.014 0 0 0-.654.463l-.546.578 2.852 3.02.546-.579a2.14 2.14 0 0 0 .437-.692 2.244 2.244 0 0 0 0-1.635ZM17.45 8.721 14.597 5.7 9.82 10.76a.54.54 0 0 0-.137.27l-.536 2.84c-.07.37.239.696.588.622l2.682-.567a.492.492 0 0 0 .255-.145l4.778-5.06Z"
              clip-rule="evenodd"
            />
          </svg>
          Edit
        </button>
        <button
          type="button"
          class="inline-flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:outline-none focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
            <path
              fill-rule="evenodd"
              d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
              clip-rule="evenodd"
            />
          </svg>

          Delete
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete product modal -->
<div id="deleteProductModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-md p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-5">
      <button
        type="button"
        class="absolute right-2.5 top-2.5 ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
        data-modal-toggle="deleteProductModal"
      >
        <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
      <h3 class="mb-1 text-lg font-semibold text-gray-900 dark:text-white">Are you sure?</h3>
      <p class="mb-4 text-gray-500 dark:text-gray-400">You are about to delete the following product, this cannot be undone:</p>
      <ul role="list" class="mb-4 space-y-2 text-left text-gray-500 dark:text-gray-400 sm:mb-5">
        <li class="flex items-center space-x-2">
          <svg class="h-4 w-4 shrink-0 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm9.4-5.5a1 1 0 1 0 0 2 1 1 0 1 0 0-2ZM10 10a1 1 0 1 0 0 2h1v3h-1a1 1 0 1 0 0 2h4a1 1 0 1 0 0-2h-1v-4c0-.6-.4-1-1-1h-2Z" clip-rule="evenodd" />
          </svg>
          <span>Apple iMac 27”</span>
        </li>
      </ul>
      <div class="flex items-center space-x-4">
        <button
          data-modal-toggle="deleteProductModal"
          type="button"
          class="rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
        >
          No, cancel
        </button>
        <button
          type="submit"
          class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path
              fill-rule="evenodd"
              d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
              clip-rule="evenodd"
            />
          </svg>
          Yes, delete
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Delete all products modal -->
<div id="deleteProductsModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-md p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-5">
      <button
        type="button"
        class="absolute right-2.5 top-2.5 ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
        data-modal-toggle="deleteProductsModal"
      >
        <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
      <h3 class="mb-1 text-lg font-semibold text-gray-900 dark:text-white">Are you sure?</h3>
      <p class="mb-4 text-gray-500 dark:text-gray-400">You are about to delete the following 3 products, this cannot be undone:</p>
      <ul role="list" class="mb-4 space-y-2 text-left text-gray-500 dark:text-gray-400 sm:mb-5">
        <li class="flex items-center space-x-2">
          <svg class="h-4 w-4 shrink-0 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm11-4a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0V8Zm-1 7a1 1 0 1 0 0 2 1 1 0 1 0 0-2Z" clip-rule="evenodd" />
          </svg>
          <span>Apple iMac 27”</span>
        </li>
        <li class="flex items-center space-x-2">
          <svg class="h-4 w-4 shrink-0 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm11-4a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0V8Zm-1 7a1 1 0 1 0 0 2 1 1 0 1 0 0-2Z" clip-rule="evenodd" />
          </svg>
          <span>Magic Keyboard</span>
        </li>
        <li class="flex items-center space-x-2">
          <svg class="h-4 w-4 shrink-0 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm11-4a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0V8Zm-1 7a1 1 0 1 0 0 2 1 1 0 1 0 0-2Z" clip-rule="evenodd" />
          </svg>
          <span>Xbox Series X</span>
        </li>
      </ul>
      <div class="flex items-center space-x-4">
        <button
          data-modal-toggle="deleteProductsModal"
          type="button"
          class="rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
        >
          No, cancel
        </button>
        <button
          type="submit"
          class="flex items-center rounded-lg bg-red-700 px-3 py-2 text-center text-sm font-medium text-white hover:bg-red-800 focus:ring-4 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-900"
        >
          <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path
              fill-rule="evenodd"
              d="M8.6 2.6A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4c0-.5.2-1 .6-1.4ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z"
              clip-rule="evenodd"
            />
          </svg>
          Yes, delete
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Archive product modal -->
<div id="archiveModal" tabindex="-1" aria-hidden="true" class="fixed left-0 right-0 top-0 z-50 hidden h-[calc(100%-1rem)] max-h-full w-full items-center justify-center overflow-y-auto overflow-x-hidden md:inset-0">
  <div class="relative max-h-full w-full max-w-md p-4">
    <!-- Modal content -->
    <div class="relative rounded-lg bg-white p-4 text-center shadow-sm dark:bg-gray-800 sm:p-5">
      <button
        type="button"
        class="absolute right-2.5 top-2.5 ml-auto inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-gray-400 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-600 dark:hover:text-white"
        data-modal-toggle="archiveModal"
      >
        <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6" />
        </svg>
        <span class="sr-only">Close modal</span>
      </button>
      <svg class="mx-auto mb-3.5 h-11 w-11 text-gray-400 dark:text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
        <path fill-rule="evenodd" d="M20 10H4v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8ZM9 13v-1h6v1c0 .6-.4 1-1 1h-4a1 1 0 0 1-1-1Z" clip-rule="evenodd" />
        <path d="M2 6c0-1.1.9-2 2-2h16a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Z" />
      </svg>
      <p class="mb-4 text-gray-500 dark:text-gray-300">Are you sure you want to archive these items?</p>
      <div class="flex items-center justify-center space-x-4">
        <button
          data-modal-toggle="archiveModal"
          type="button"
          class="rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
        >
          No, cancel
        </button>
        <button
          type="submit"
          class="rounded-lg bg-primary-700 px-3 py-2 text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
        >
          Yes, I do
        </button>
      </div>
    </div>
  </div>
</div>
