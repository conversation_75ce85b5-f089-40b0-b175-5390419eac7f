/*!*********************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/app.css ***!
  \*********************************************************************************************************/
/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: 'Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: 'ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace';
    --color-red-50: #FEF2F2;
    --color-red-100: #FEE2E2;
    --color-red-200: #FECACA;
    --color-red-300: #FCA5A5;
    --color-red-400: #F87171;
    --color-red-500: #EF4444;
    --color-red-600: #DC2626;
    --color-red-700: #B91C1C;
    --color-red-800: #991B1B;
    --color-red-900: #7F1D1D;
    --color-red-950: oklch(25.8% 0.092 26.042);
    --color-orange-50: #FFFAF0;
    --color-orange-100: #FEEBC8;
    --color-orange-200: #FBD38D;
    --color-orange-300: #F6AD55;
    --color-orange-400: #ED8936;
    --color-orange-500: #DD6B20;
    --color-orange-600: #C05621;
    --color-orange-700: #9C4221;
    --color-orange-800: #7B341E;
    --color-orange-900: #652B19;
    --color-orange-950: oklch(26.6% 0.079 36.259);
    --color-amber-50: oklch(98.7% 0.022 95.277);
    --color-amber-100: oklch(96.2% 0.059 95.617);
    --color-amber-200: oklch(92.4% 0.12 95.746);
    --color-amber-300: oklch(87.9% 0.169 91.605);
    --color-amber-400: oklch(82.8% 0.189 84.429);
    --color-amber-500: oklch(76.9% 0.188 70.08);
    --color-amber-600: oklch(66.6% 0.179 58.318);
    --color-amber-700: oklch(55.5% 0.163 48.998);
    --color-amber-800: oklch(47.3% 0.137 46.201);
    --color-amber-900: oklch(41.4% 0.112 45.904);
    --color-amber-950: oklch(27.9% 0.077 45.635);
    --color-yellow-50: #FFFBEB;
    --color-yellow-100: #FEF3C7;
    --color-yellow-200: #FDE68A;
    --color-yellow-300: #FCD34D;
    --color-yellow-400: #FBBF24;
    --color-yellow-500: #F59E0B;
    --color-yellow-600: #D97706;
    --color-yellow-700: #B45309;
    --color-yellow-800: #92400E;
    --color-yellow-900: #78350F;
    --color-yellow-950: oklch(28.6% 0.066 53.813);
    --color-lime-50: oklch(98.6% 0.031 120.757);
    --color-lime-100: oklch(96.7% 0.067 122.328);
    --color-lime-200: oklch(93.8% 0.127 124.321);
    --color-lime-300: oklch(89.7% 0.196 126.665);
    --color-lime-400: oklch(84.1% 0.238 128.85);
    --color-lime-500: oklch(76.8% 0.233 130.85);
    --color-lime-600: oklch(64.8% 0.2 131.684);
    --color-lime-700: oklch(53.2% 0.157 131.589);
    --color-lime-800: oklch(45.3% 0.124 130.933);
    --color-lime-900: oklch(40.5% 0.101 131.063);
    --color-lime-950: oklch(27.4% 0.072 132.109);
    --color-green-50: #ECFDF5;
    --color-green-100: #D1FAE5;
    --color-green-200: #A7F3D0;
    --color-green-300: #6EE7B7;
    --color-green-400: #34D399;
    --color-green-500: #10B981;
    --color-green-600: #059669;
    --color-green-700: #047857;
    --color-green-800: #065F46;
    --color-green-900: #064E3B;
    --color-green-950: oklch(26.6% 0.065 152.934);
    --color-emerald-50: oklch(97.9% 0.021 166.113);
    --color-emerald-100: oklch(95% 0.052 163.051);
    --color-emerald-200: oklch(90.5% 0.093 164.15);
    --color-emerald-300: oklch(84.5% 0.143 164.978);
    --color-emerald-400: oklch(76.5% 0.177 163.223);
    --color-emerald-500: oklch(69.6% 0.17 162.48);
    --color-emerald-600: oklch(59.6% 0.145 163.225);
    --color-emerald-700: oklch(50.8% 0.118 165.612);
    --color-emerald-800: oklch(43.2% 0.095 166.913);
    --color-emerald-900: oklch(37.8% 0.077 168.94);
    --color-emerald-950: oklch(26.2% 0.051 172.552);
    --color-teal-50: #F0FDFA;
    --color-teal-100: #CCFBF1;
    --color-teal-200: #99F6E4;
    --color-teal-300: #5EEAD4;
    --color-teal-400: #2DD4BF;
    --color-teal-500: #14B8A6;
    --color-teal-600: #0D9488;
    --color-teal-700: #0F766E;
    --color-teal-800: #115E59;
    --color-teal-900: #134E4A;
    --color-teal-950: oklch(27.7% 0.046 192.524);
    --color-cyan-50: #ECFEFF;
    --color-cyan-100: #CFFAFE;
    --color-cyan-200: #A5F3FC;
    --color-cyan-300: #67E8F9;
    --color-cyan-400: #22D3EE;
    --color-cyan-500: #06B6D4;
    --color-cyan-600: #0891B2;
    --color-cyan-700: #0E7490;
    --color-cyan-800: #155E75;
    --color-cyan-900: #164E63;
    --color-cyan-950: oklch(30.2% 0.056 229.695);
    --color-sky-50: oklch(97.7% 0.013 236.62);
    --color-sky-100: oklch(95.1% 0.026 236.824);
    --color-sky-200: oklch(90.1% 0.058 230.902);
    --color-sky-300: oklch(82.8% 0.111 230.318);
    --color-sky-400: oklch(74.6% 0.16 232.661);
    --color-sky-500: oklch(68.5% 0.169 237.323);
    --color-sky-600: oklch(58.8% 0.158 241.966);
    --color-sky-700: oklch(50% 0.134 242.749);
    --color-sky-800: oklch(44.3% 0.11 240.79);
    --color-sky-900: oklch(39.1% 0.09 240.876);
    --color-sky-950: oklch(29.3% 0.066 243.157);
    --color-blue-50: #EFF6FF;
    --color-blue-100: #DBEAFE;
    --color-blue-200: #BFDBFE;
    --color-blue-300: #93C5FD;
    --color-blue-400: #60A5FA;
    --color-blue-500: #3B82F6;
    --color-blue-600: #2563EB;
    --color-blue-700: #1D4ED8;
    --color-blue-800: #1E40AF;
    --color-blue-900: #1E3A8A;
    --color-blue-950: oklch(28.2% 0.091 267.935);
    --color-indigo-50: #EEF2FF;
    --color-indigo-100: #E0E7FF;
    --color-indigo-200: #C7D2FE;
    --color-indigo-300: #A5B4FC;
    --color-indigo-400: #818CF8;
    --color-indigo-500: #6366F1;
    --color-indigo-600: #4F46E5;
    --color-indigo-700: #4338CA;
    --color-indigo-800: #3730A3;
    --color-indigo-900: #312E81;
    --color-indigo-950: oklch(25.7% 0.09 281.288);
    --color-violet-50: oklch(96.9% 0.016 293.756);
    --color-violet-100: oklch(94.3% 0.029 294.588);
    --color-violet-200: oklch(89.4% 0.057 293.283);
    --color-violet-300: oklch(81.1% 0.111 293.571);
    --color-violet-400: oklch(70.2% 0.183 293.541);
    --color-violet-500: oklch(60.6% 0.25 292.717);
    --color-violet-600: oklch(54.1% 0.281 293.009);
    --color-violet-700: oklch(49.1% 0.27 292.581);
    --color-violet-800: oklch(43.2% 0.232 292.759);
    --color-violet-900: oklch(38% 0.189 293.745);
    --color-violet-950: oklch(28.3% 0.141 291.089);
    --color-purple-50: #F5F3FF;
    --color-purple-100: #EDE9FE;
    --color-purple-200: #DDD6FE;
    --color-purple-300: #C4B5FD;
    --color-purple-400: #A78BFA;
    --color-purple-500: #8B5CF6;
    --color-purple-600: #7C3AED;
    --color-purple-700: #6D28D9;
    --color-purple-800: #5B21B6;
    --color-purple-900: #4C1D95;
    --color-purple-950: oklch(29.1% 0.149 302.717);
    --color-fuchsia-50: oklch(97.7% 0.017 320.058);
    --color-fuchsia-100: oklch(95.2% 0.037 318.852);
    --color-fuchsia-200: oklch(90.3% 0.076 319.62);
    --color-fuchsia-300: oklch(83.3% 0.145 321.434);
    --color-fuchsia-400: oklch(74% 0.238 322.16);
    --color-fuchsia-500: oklch(66.7% 0.295 322.15);
    --color-fuchsia-600: oklch(59.1% 0.293 322.896);
    --color-fuchsia-700: oklch(51.8% 0.253 323.949);
    --color-fuchsia-800: oklch(45.2% 0.211 324.591);
    --color-fuchsia-900: oklch(40.1% 0.17 325.612);
    --color-fuchsia-950: oklch(29.3% 0.136 325.661);
    --color-pink-50: #FDF2F8;
    --color-pink-100: #FCE7F3;
    --color-pink-200: #FBCFE8;
    --color-pink-300: #F9A8D4;
    --color-pink-400: #F472B6;
    --color-pink-500: #EC4899;
    --color-pink-600: #DB2777;
    --color-pink-700: #BE185D;
    --color-pink-800: #9D174D;
    --color-pink-900: #831843;
    --color-pink-950: oklch(28.4% 0.109 3.907);
    --color-rose-50: oklch(96.9% 0.015 12.422);
    --color-rose-100: oklch(94.1% 0.03 12.58);
    --color-rose-200: oklch(89.2% 0.058 10.001);
    --color-rose-300: oklch(81% 0.117 11.638);
    --color-rose-400: oklch(71.2% 0.194 13.428);
    --color-rose-500: oklch(64.5% 0.246 16.439);
    --color-rose-600: oklch(58.6% 0.253 17.585);
    --color-rose-700: oklch(51.4% 0.222 16.935);
    --color-rose-800: oklch(45.5% 0.188 13.697);
    --color-rose-900: oklch(41% 0.159 10.272);
    --color-rose-950: oklch(27.1% 0.105 12.094);
    --color-slate-50: oklch(98.4% 0.003 247.858);
    --color-slate-100: oklch(96.8% 0.007 247.896);
    --color-slate-200: oklch(92.9% 0.013 255.508);
    --color-slate-300: oklch(86.9% 0.022 252.894);
    --color-slate-400: oklch(70.4% 0.04 256.788);
    --color-slate-500: oklch(55.4% 0.046 257.417);
    --color-slate-600: oklch(44.6% 0.043 257.281);
    --color-slate-700: oklch(37.2% 0.044 257.287);
    --color-slate-800: oklch(27.9% 0.041 260.031);
    --color-slate-900: oklch(20.8% 0.042 265.755);
    --color-slate-950: oklch(12.9% 0.042 264.695);
    --color-gray-50: #F9FAFB;
    --color-gray-100: #F3F4F6;
    --color-gray-200: #E5E7EB;
    --color-gray-300: #D1D5DB;
    --color-gray-400: #9CA3AF;
    --color-gray-500: #6B7280;
    --color-gray-600: #4B5563;
    --color-gray-700: #374151;
    --color-gray-800: #1F2937;
    --color-gray-900: #111827;
    --color-gray-950: oklch(13% 0.028 261.692);
    --color-zinc-50: oklch(98.5% 0 0);
    --color-zinc-100: oklch(96.7% 0.001 286.375);
    --color-zinc-200: oklch(92% 0.004 286.32);
    --color-zinc-300: oklch(87.1% 0.006 286.286);
    --color-zinc-400: oklch(70.5% 0.015 286.067);
    --color-zinc-500: oklch(55.2% 0.016 285.938);
    --color-zinc-600: oklch(44.2% 0.017 285.786);
    --color-zinc-700: oklch(37% 0.013 285.805);
    --color-zinc-800: oklch(27.4% 0.006 286.033);
    --color-zinc-900: oklch(21% 0.006 285.885);
    --color-zinc-950: oklch(14.1% 0.005 285.823);
    --color-neutral-50: oklch(98.5% 0 0);
    --color-neutral-100: oklch(97% 0 0);
    --color-neutral-200: oklch(92.2% 0 0);
    --color-neutral-300: oklch(87% 0 0);
    --color-neutral-400: oklch(70.8% 0 0);
    --color-neutral-500: oklch(55.6% 0 0);
    --color-neutral-600: oklch(43.9% 0 0);
    --color-neutral-700: oklch(37.1% 0 0);
    --color-neutral-800: oklch(26.9% 0 0);
    --color-neutral-900: oklch(20.5% 0 0);
    --color-neutral-950: oklch(14.5% 0 0);
    --color-stone-50: oklch(98.5% 0.001 106.423);
    --color-stone-100: oklch(97% 0.001 106.424);
    --color-stone-200: oklch(92.3% 0.003 48.717);
    --color-stone-300: oklch(86.9% 0.005 56.366);
    --color-stone-400: oklch(70.9% 0.01 56.259);
    --color-stone-500: oklch(55.3% 0.013 58.071);
    --color-stone-600: oklch(44.4% 0.011 73.639);
    --color-stone-700: oklch(37.4% 0.01 67.558);
    --color-stone-800: oklch(26.8% 0.007 34.298);
    --color-stone-900: oklch(21.6% 0.006 56.043);
    --color-stone-950: oklch(14.7% 0.004 49.25);
    --color-black: #000000;
    --color-white: #ffffff;
    --spacing: 0.25rem;
    --breakpoint-sm: 40rem;
    --breakpoint-md: 48rem;
    --breakpoint-lg: 64rem;
    --breakpoint-xl: 80rem;
    --breakpoint-2xl: 96rem;
    --container-3xs: 16rem;
    --container-2xs: 18rem;
    --container-xs: 20rem;
    --container-sm: 24rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-xl: 36rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-5xl: 64rem;
    --container-6xl: 72rem;
    --container-7xl: 80rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --text-7xl: 4.5rem;
    --text-7xl--line-height: 1;
    --text-8xl: 6rem;
    --text-8xl--line-height: 1;
    --text-9xl: 8rem;
    --text-9xl--line-height: 1;
    --font-weight-thin: 100;
    --font-weight-extralight: 200;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-4xl: 2rem;
    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);
    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);
    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);
    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);
    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);
    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);
    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);
    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);
    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);
    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);
    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);
    --text-shadow-sm: 0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),
      0px 2px 2px rgb(0 0 0 / 0.075);
    --text-shadow-md: 0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),
      0px 2px 4px rgb(0 0 0 / 0.1);
    --text-shadow-lg: 0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),
      0px 4px 8px rgb(0 0 0 / 0.1);
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --animate-spin: spin 1s linear infinite;
    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --animate-bounce: bounce 1s infinite;
    --blur-xs: 4px;
    --blur-sm: 8px;
    --blur-md: 12px;
    --blur-lg: 16px;
    --blur-xl: 24px;
    --blur-2xl: 40px;
    --blur-3xl: 64px;
    --perspective-dramatic: 100px;
    --perspective-near: 300px;
    --perspective-normal: 500px;
    --perspective-midrange: 800px;
    --perspective-distant: 1200px;
    --aspect-video: 16 / 9;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-transparent: transparent;
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;
    --font-body: 'Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif', 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .pointer-events-none {
    pointer-events: none;
  }
  .\!visible {
    visibility: visible !important;
  }
  .collapse {
    visibility: collapse;
  }
  .invisible {
    visibility: hidden;
  }
  .visible {
    visibility: visible;
  }
  .datatable-wrapper {
    width: 100%;
    & .datatable-top {
      display: flex;
      justify-content: space-between;
      flex-direction: column-reverse;
      align-items: start;
      gap: 1rem;
      margin-bottom: 1rem;
      @media (min-width: 640px) {
        flex-direction: row-reverse;
        align-items: center;
      }
    }
    & .datatable-search .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    & .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark & .datatable-search .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .dark & .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & thead th .datatable-input {
      background-color: white;
      font-weight: 400;
      color: var(--color-gray-900);
      padding-top: .35rem;
      padding-bottom: .35rem;
      min-width: 0;
    }
    .dark & thead th .datatable-input {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    & .datatable-top .datatable-dropdown {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark & .datatable-top .datatable-dropdown {
      color: var(--color-gray-400);
    }
    & .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark & .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
    & .datatable-container thead tr.search-filtering-row th {
      padding-top: 0;
    }
    & .datatable-search .datatable-input:focus {
      border-color: var(--color-blue-600);
    }
    & .datatable-container {
      overflow-x: auto;
    }
    & .datatable-table {
      width: 100%;
      font-size: 0.875rem;
      color: var(--color-gray-500);
      text-align: left;
    }
    .dark & .datatable-table {
      color: var(--color-gray-400);
    }
    & .datatable-table thead {
      font-size: 0.75rem;
      color: var(--color-gray-500);
      background-color: var(--color-gray-50);
    }
    .dark & .datatable-table thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    & .datatable-table thead th {
      white-space: nowrap;
    }
    & .datatable-table thead th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table tbody th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table tbody td {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    & .datatable-table thead th .datatable-sorter {
      text-transform: uppercase;
    }
    & .datatable-table thead th {
      text-transform: uppercase;
    }
    & .datatable-table thead th .datatable-sorter:hover {
      color: var(--color-gray-900);
    }
    & .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: var(--color-gray-900);
    }
    & .datatable-table thead th.datatable-descending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark & .datatable-table thead th .datatable-sorter:hover {
      color: white;
    }
    .dark & .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    .dark & .datatable-table thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    & .datatable-table tbody tr.selected {
      background-color: var(--color-gray-100);
    }
    .dark & .datatable-table tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    & .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-200);
    }
    .dark & .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    & .datatable-table .datatable-empty {
      text-align: center;
    }
    & .datatable-bottom {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: start;
      margin-top: 1rem;
      gap: 1rem;
      @media (min-width: 640px) {
        flex-direction: row;
        align-items: center;
      }
    }
    & .datatable-bottom .datatable-info {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark & .datatable-bottom .datatable-info {
      color: var(--color-gray-400);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark & .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-bottom {
    .datatable-wrapper & {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: start;
      margin-top: 1rem;
      gap: 1rem;
      @media (min-width: 640px) {
        flex-direction: row;
        align-items: center;
      }
    }
    .datatable-wrapper & .datatable-info {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper & .datatable-info {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper & .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-pagination {
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper .datatable-bottom & .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-pagination-list-item-link {
    .datatable-wrapper .datatable-bottom .datatable-pagination & {
      display: flex;
      align-items: center;
      color: var(--color-gray-500);
      font-weight: 500;
      padding-left: 0.75rem;
      padding-right: 0.75rem;
      height: 2rem;
      font-size: 0.875rem;
      border-top: 1px solid var(--color-gray-300);
      border-bottom: 1px solid var(--color-gray-300);
      border-right: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination & {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type &:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type & {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type & {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:hover {
      background-color: var(--color-gray-50);
      color: var(--color-gray-700);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .datatable-pagination-list-item {
    .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type {
      position: relative;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      left: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(-50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
      position: absolute;
      top: 50%;
      right: 50%;
      width: 1.3rem;
      height: 1.3rem;
      transform: translate(50%, -50%);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link {
      border-top-left-radius: 0.5rem;
      border-bottom-left-radius: 0.5rem;
      border-left: 1px solid var(--color-gray-300);
    }
    .dark .datatable-wrapper .datatable-bottom .datatable-pagination &:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper .datatable-bottom .datatable-pagination &:last-of-type .datatable-pagination-list-item-link {
      border-top-right-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
      border-left: 0;
    }
  }
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
  .absolute {
    position: absolute;
  }
  .fixed {
    position: fixed;
  }
  .relative {
    position: relative;
  }
  .static {
    position: static;
  }
  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }
  .inset-y-0 {
    inset-block: calc(var(--spacing) * 0);
  }
  .-start-1\.5 {
    inset-inline-start: calc(var(--spacing) * -1.5);
  }
  .-start-3 {
    inset-inline-start: calc(var(--spacing) * -3);
  }
  .start-0 {
    inset-inline-start: calc(var(--spacing) * 0);
  }
  .start-1\/3 {
    inset-inline-start: calc(1/3 * 100%);
  }
  .start-2\/3 {
    inset-inline-start: calc(2/3 * 100%);
  }
  .start-4 {
    inset-inline-start: calc(var(--spacing) * 4);
  }
  .start-6 {
    inset-inline-start: calc(var(--spacing) * 6);
  }
  .end-0 {
    inset-inline-end: calc(var(--spacing) * 0);
  }
  .end-1 {
    inset-inline-end: calc(var(--spacing) * 1);
  }
  .end-2 {
    inset-inline-end: calc(var(--spacing) * 2);
  }
  .end-2\.5 {
    inset-inline-end: calc(var(--spacing) * 2.5);
  }
  .end-4 {
    inset-inline-end: calc(var(--spacing) * 4);
  }
  .top-0 {
    top: calc(var(--spacing) * 0);
  }
  .top-1 {
    top: calc(var(--spacing) * 1);
  }
  .top-1\/2 {
    top: calc(1/2 * 100%);
  }
  .top-2 {
    top: calc(var(--spacing) * 2);
  }
  .top-2\.5 {
    top: calc(var(--spacing) * 2.5);
  }
  .top-3 {
    top: calc(var(--spacing) * 3);
  }
  .top-4 {
    top: calc(var(--spacing) * 4);
  }
  .right-0 {
    right: calc(var(--spacing) * 0);
  }
  .right-2 {
    right: calc(var(--spacing) * 2);
  }
  .right-2\.5 {
    right: calc(var(--spacing) * 2.5);
  }
  .right-4 {
    right: calc(var(--spacing) * 4);
  }
  .right-6 {
    right: calc(var(--spacing) * 6);
  }
  .\!bottom-0 {
    bottom: calc(var(--spacing) * 0) !important;
  }
  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }
  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }
  .bottom-1 {
    bottom: calc(var(--spacing) * 1);
  }
  .bottom-2\.5 {
    bottom: calc(var(--spacing) * 2.5);
  }
  .bottom-3 {
    bottom: calc(var(--spacing) * 3);
  }
  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }
  .bottom-5 {
    bottom: calc(var(--spacing) * 5);
  }
  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }
  .bottom-\[60px\] {
    bottom: 60px;
  }
  .\!left-0 {
    left: calc(var(--spacing) * 0) !important;
  }
  .left-0 {
    left: calc(var(--spacing) * 0);
  }
  .left-1 {
    left: calc(var(--spacing) * 1);
  }
  .left-1\/2 {
    left: calc(1/2 * 100%);
  }
  .left-2 {
    left: calc(var(--spacing) * 2);
  }
  .left-4 {
    left: calc(var(--spacing) * 4);
  }
  .left-5 {
    left: calc(var(--spacing) * 5);
  }
  .left-6 {
    left: calc(var(--spacing) * 6);
  }
  .-z-10 {
    z-index: calc(10 * -1);
  }
  .z-0 {
    z-index: 0;
  }
  .z-10 {
    z-index: 10;
  }
  .z-20 {
    z-index: 20;
  }
  .z-30 {
    z-index: 30;
  }
  .z-40 {
    z-index: 40;
  }
  .z-50 {
    z-index: 50;
  }
  .order-1 {
    order: 1;
  }
  .order-2 {
    order: 2;
  }
  .order-3 {
    order: 3;
  }
  .col-span-1 {
    grid-column: span 1 / span 1;
  }
  .col-span-2 {
    grid-column: span 2 / span 2;
  }
  .col-span-3 {
    grid-column: span 3 / span 3;
  }
  .col-span-6 {
    grid-column: span 6 / span 6;
  }
  .col-span-12 {
    grid-column: span 12 / span 12;
  }
  .col-span-full {
    grid-column: 1 / -1;
  }
  .container {
    width: 100%;
    @media (width >= 40rem) {
      max-width: 40rem;
    }
    @media (width >= 48rem) {
      max-width: 48rem;
    }
    @media (width >= 64rem) {
      max-width: 64rem;
    }
    @media (width >= 80rem) {
      max-width: 80rem;
    }
    @media (width >= 96rem) {
      max-width: 96rem;
    }
  }
  .format {
    color: var(--tw-format-body);
    max-width: 65ch;
    :where([class~="lead"]):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-lead);
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(a):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-links);
      text-decoration: underline;
      font-weight: 500;
      &:hover {
        text-decoration: none;
      }
    }
    :where(strong):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-bold);
      font-weight: 700;
    }
    :where(a strong):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(blockquote strong):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(thead th strong):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(ol):not(:where([class~="not-format"] *)) {
      list-style-type: decimal;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-left: 1.625em;
    }
    :where(ol[type="A"]):not(:where([class~="not-format"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a"]):not(:where([class~="not-format"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="A" s]):not(:where([class~="not-format"] *)) {
      list-style-type: upper-alpha;
    }
    :where(ol[type="a" s]):not(:where([class~="not-format"] *)) {
      list-style-type: lower-alpha;
    }
    :where(ol[type="I"]):not(:where([class~="not-format"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i"]):not(:where([class~="not-format"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="I" s]):not(:where([class~="not-format"] *)) {
      list-style-type: upper-roman;
    }
    :where(ol[type="i" s]):not(:where([class~="not-format"] *)) {
      list-style-type: lower-roman;
    }
    :where(ol[type="1"]):not(:where([class~="not-format"] *)) {
      list-style-type: decimal;
    }
    :where(ul):not(:where([class~="not-format"] *)) {
      list-style-type: disc;
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-left: 1.625em;
    }
    :where(ol > li):not(:where([class~="not-format"] *))::marker {
      font-weight: 400;
      color: var(--tw-format-counters);
    }
    :where(ul > li):not(:where([class~="not-format"] *))::marker {
      color: var(--tw-format-bullets);
    }
    :where(hr):not(:where([class~="not-format"] *)) {
      border-color: var(--tw-format-hr);
      border-top-width: 1;
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(blockquote):not(:where([class~="not-format"] *)) {
      font-size: 1.1111111em;
      font-weight: 700;
      font-style: italic;
      color: var(--tw-format-quotes);
      quotes: "\201C""\201D""\2018""\2019";
      margin-bottom: 1.6em;
    }
    :where(blockquote):not(:where([class~="not-format"] *))::before {
      content: "";
      background-image: url("data:image/svg+xml,%0A%3Csvg width=%2732%27 height=%2724%27 viewBox=%270 0 32 24%27 fill=%27none%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Cpath d=%27M18.6893 24V14.1453C18.6893 6.54 23.664 1.38533 30.6667 -7.15256e-07L31.9933 2.868C28.7507 4.09066 26.6667 7.71867 26.6667 10.6667H32V24H18.6893ZM-9.53674e-07 24V14.1453C-9.53674e-07 6.54 4.99733 1.384 12 -7.15256e-07L13.328 2.868C10.084 4.09066 8 7.71867 8 10.6667L13.3107 10.6667V24H-9.53674e-07Z%27 fill=%27%239CA3AF%27/%3E%3C/svg%3E%0A");
      background-repeat: no-repeat;
      color: var(--tw-format-quotes);
      width: 1.7777778em;
      height: 1.3333333em;
      display: block;
      margin-top: 1.6em;
    }
    :where(blockquote p:first-of-type):not(:where([class~="not-format"] *))::before {
      content: open-quote;
    }
    :where(blockquote p:last-of-type):not(:where([class~="not-format"] *))::after {
      content: close-quote;
    }
    :where(h1):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-headings);
      font-weight: 800;
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h1 strong):not(:where([class~="not-format"] *)) {
      font-weight: 900;
      color: inherit;
    }
    :where(h2):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-headings);
      font-weight: 700;
      font-size: 1.5em;
      margin-top: 0;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h2 strong):not(:where([class~="not-format"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h3):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-headings);
      font-weight: 700;
      font-size: 1.25em;
      margin-top: 0;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h3 strong):not(:where([class~="not-format"] *)) {
      font-weight: 800;
      color: inherit;
    }
    :where(h4):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-headings);
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(h4 strong):not(:where([class~="not-format"] *)) {
      font-weight: 700;
      color: inherit;
    }
    :where(img):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(figure > *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-captions);
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    :where(code):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-code);
      font-weight: 600;
      background-color: var(--tw-format-code-bg);
      padding-top: 0.3333333em;
      padding-bottom: 0.3333333em;
      padding-left: 0.5555556em;
      padding-right: 0.5555556em;
      border-radius: 0.2222222em;
      font-size: 0.875em;
    }
    :where(a code):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(h1 code):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(h2 code):not(:where([class~="not-format"] *)) {
      color: inherit;
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-format"] *)) {
      color: inherit;
      font-size: 0.9em;
    }
    :where(h4 code):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(blockquote code):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(thead th code):not(:where([class~="not-format"] *)) {
      color: inherit;
    }
    :where(pre):not(:where([class~="not-format"] *)) {
      color: var(--tw-format-pre-code);
      background-color: var(--tw-format-pre-bg);
      overflow-x: auto;
      font-weight: 400;
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-right: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-left: 1.1428571em;
    }
    :where(pre code):not(:where([class~="not-format"] *)) {
      background-color: transparent;
      border-width: 0;
      border-radius: 0;
      padding: 0;
      font-weight: inherit;
      color: inherit;
      font-size: inherit;
      font-family: inherit;
      line-height: inherit;
    }
    :where(pre code):not(:where([class~="not-format"] *))::before {
      content: none;
    }
    :where(pre code):not(:where([class~="not-format"] *))::after {
      content: none;
    }
    :where(table):not(:where([class~="not-format"] *)) {
      width: 100%;
      table-layout: auto;
      text-align: left;
      margin-top: 2em;
      margin-bottom: 2em;
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead):not(:where([class~="not-format"] *)) {
      background-color: var(--tw-format-th-bg);
      border-radius: 0.2777778em;
    }
    :where(thead th):not(:where([class~="not-format"] *)) {
      background-color: var(--tw-format-th-bg);
      color: var(--tw-format-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding: 0.5555556em;
      padding-right: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-left: 0.5714286em;
    }
    :where(tbody th):not(:where([class~="not-format"] *)) {
      background-color: var(--tw-format-th-bg);
      color: var(--tw-format-headings);
      font-weight: 600;
      vertical-align: bottom;
      padding: 0.5555556em;
    }
    :where(tbody tr th p, tbody tr td p):not(:where([class~="not-format"] *)) {
      margin: 0 !important;
    }
    :where(tbody tr th, tbody tr td):not(:where([class~="not-format"] *)) {
      padding: 0.6666667em !important;
    }
    :where(tbody tr):not(:where([class~="not-format"] *)) {
      border-bottom-width: 1px;
      border-bottom-color: var(--tw-format-td-borders);
    }
    :where(tbody tr:last-child):not(:where([class~="not-format"] *)) {
      border-bottom-width: 0;
    }
    :where(tbody td):not(:where([class~="not-format"] *)) {
      vertical-align: baseline;
    }
    :where(tfoot):not(:where([class~="not-format"] *)) {
      border-top-width: 1px;
      border-top-color: var(--tw-format-th-borders);
    }
    :where(tfoot td):not(:where([class~="not-format"] *)) {
      vertical-align: top;
    }
    --tw-format-body: oklch(55.1% 0.027 264.364);
    --tw-format-headings: oklch(21% 0.034 264.665);
    --tw-format-lead: oklch(55.1% 0.027 264.364);
    --tw-format-links: oklch(44.6% 0.03 256.802);
    --tw-format-bold: oklch(21% 0.034 264.665);
    --tw-format-counters: oklch(55.1% 0.027 264.364);
    --tw-format-bullets: oklch(55.1% 0.027 264.364);
    --tw-format-hr: oklch(92.8% 0.006 264.531);
    --tw-format-quotes: oklch(21% 0.034 264.665);
    --tw-format-quote-borders: oklch(92.8% 0.006 264.531);
    --tw-format-captions: oklch(55.1% 0.027 264.364);
    --tw-format-code: oklch(21% 0.034 264.665);
    --tw-format-code-bg: oklch(96.7% 0.003 264.542);
    --tw-format-pre-code: oklch(44.6% 0.03 256.802);
    --tw-format-pre-bg: oklch(96.7% 0.003 264.542);
    --tw-format-th-borders: oklch(92.8% 0.006 264.531);
    --tw-format-th-bg: oklch(98.5% 0.002 247.839);
    --tw-format-td-borders: oklch(92.8% 0.006 264.531);
    --tw-format-invert-body: oklch(70.7% 0.022 261.325);
    --tw-format-invert-headings: #fff;
    --tw-format-invert-lead: oklch(70.7% 0.022 261.325);
    --tw-format-invert-links: #fff;
    --tw-format-invert-bold: #fff;
    --tw-format-invert-counters: oklch(70.7% 0.022 261.325);
    --tw-format-invert-bullets: oklch(44.6% 0.03 256.802);
    --tw-format-invert-hr: oklch(37.3% 0.034 259.733);
    --tw-format-invert-quotes: oklch(96.7% 0.003 264.542);
    --tw-format-invert-quote-borders: oklch(37.3% 0.034 259.733);
    --tw-format-invert-captions: oklch(70.7% 0.022 261.325);
    --tw-format-invert-code: #fff;
    --tw-format-invert-code-bg: oklch(27.8% 0.033 256.848);
    --tw-format-invert-pre-code: oklch(87.2% 0.01 258.338);
    --tw-format-invert-pre-bg: oklch(37.3% 0.034 259.733);
    --tw-format-invert-th-borders: oklch(44.6% 0.03 256.802);
    --tw-format-invert-td-borders: oklch(37.3% 0.034 259.733);
    --tw-format-invert-th-bg: oklch(37.3% 0.034 259.733);
    font-size: 1rem;
    line-height: 1.75;
    :where(p):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where(blockquote > p:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(video):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(figure):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(li):not(:where([class~="not-format"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
    }
    :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
    }
    :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-format"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(hr + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-format"] *)) {
      padding-top: 0.5714286em;
      padding-right: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-left: 0.5714286em;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(.format > :first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(.format > :last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 0;
    }
  }
  .format-sm {
    font-size: 1rem;
    line-height: 1.7142857;
    :where(p):not(:where([class~="not-format"] *)) {
      margin-top: 1em;
      margin-bottom: 1em;
    }
    :where([class~="lead"]):not(:where([class~="not-format"] *)) {
      font-size: 1.125em;
      line-height: 1.5555556;
      margin-top: 0.8888889em;
      margin-bottom: 0.8888889em;
    }
    :where(blockquote):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.3333333em;
    }
    :where(blockquote):not(:where([class~="not-format"] *))::before {
      margin-top: 1.3333333em;
    }
    :where(h1):not(:where([class~="not-format"] *)) {
      font-size: 1.6666667em;
      margin-top: 0;
      margin-bottom: 0.8em;
      line-height: 1.2;
    }
    :where(h2):not(:where([class~="not-format"] *)) {
      font-size: 1.5em;
      margin-top: 0;
      margin-bottom: 0.8em;
      line-height: 1.4;
    }
    :where(h3):not(:where([class~="not-format"] *)) {
      font-size: 1.25em;
      margin-top: 0;
      margin-bottom: 0.4444444em;
      line-height: 1.5555556;
    }
    :where(h4):not(:where([class~="not-format"] *)) {
      font-size: 1.125em;
      margin-top: 0;
      margin-bottom: 0.5em;
      line-height: 1.25;
    }
    :where(img):not(:where([class~="not-format"] *)) {
      margin-top: 1.5em;
      margin-bottom: 1.5em;
    }
    :where(video):not(:where([class~="not-format"] *)) {
      margin-top: 1.5em;
      margin-bottom: 1.5em;
    }
    :where(figure):not(:where([class~="not-format"] *)) {
      margin-top: 1.5em;
      margin-bottom: 1.5em;
    }
    :where(figure > *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.3333333;
      margin-top: 0.6666667em;
      text-align: center;
    }
    :where(code):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
    }
    :where(h2 code):not(:where([class~="not-format"] *)) {
      font-size: 0.9em;
    }
    :where(h3 code):not(:where([class~="not-format"] *)) {
      font-size: 0.8888889em;
    }
    :where(pre):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.6666667;
      margin-top: 1.6666667em;
      margin-bottom: 1.6666667em;
      border-radius: 0.25rem;
      padding-top: 0.6666667em;
      padding-right: 1em;
      padding-bottom: 0.6666667em;
      padding-left: 1em;
    }
    :where(ol):not(:where([class~="not-format"] *)) {
      margin-top: 1em;
      margin-bottom: 1em;
      padding-left: 1.375em;
    }
    :where(ul):not(:where([class~="not-format"] *)) {
      margin-top: 1em;
      margin-bottom: 1em;
      padding-left: 1.375em;
    }
    :where(li):not(:where([class~="not-format"] *)) {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
    :where(ol > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1em;
    }
    :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1em;
    }
    :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1em;
    }
    :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-format"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(hr):not(:where([class~="not-format"] *)) {
      margin-top: 2.5em;
      margin-bottom: 2.5em;
    }
    :where(hr + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(table):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.5;
    }
    :where(thead th):not(:where([class~="not-format"] *)) {
      padding-right: 1em;
      padding-bottom: 0.6666667em;
      padding-left: 1em;
    }
    :where(tbody tr th p):not(:where([class~="not-format"] *)) {
      margin: 0 !important;
    }
    :where(thead th:first-child):not(:where([class~="not-format"] *)) {
      padding-left: 0;
    }
    :where(thead th:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-format"] *)) {
      padding-top: 0.6666667em;
      padding-right: 1em;
      padding-bottom: 0.6666667em;
      padding-left: 1em;
    }
    :where(tbody td p, tfoot td p):not(:where([class~="not-format"] *)) {
      margin: 0 !important;
    }
    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-format"] *)) {
      padding-left: 0;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(.format > :first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(.format > :last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 0;
    }
  }
  .m-0 {
    margin: calc(var(--spacing) * 0);
  }
  .m-0\.5 {
    margin: calc(var(--spacing) * 0.5);
  }
  .m-1 {
    margin: calc(var(--spacing) * 1);
  }
  .m-361 {
    margin: calc(var(--spacing) * 361);
  }
  .\!mx-0 {
    margin-inline: calc(var(--spacing) * 0) !important;
  }
  .mx-0 {
    margin-inline: calc(var(--spacing) * 0);
  }
  .mx-1 {
    margin-inline: calc(var(--spacing) * 1);
  }
  .mx-1\.5 {
    margin-inline: calc(var(--spacing) * 1.5);
  }
  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }
  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }
  .mx-5 {
    margin-inline: calc(var(--spacing) * 5);
  }
  .mx-auto {
    margin-inline: auto;
  }
  .my-0\.5 {
    margin-block: calc(var(--spacing) * 0.5);
  }
  .my-2 {
    margin-block: calc(var(--spacing) * 2);
  }
  .my-2\.5 {
    margin-block: calc(var(--spacing) * 2.5);
  }
  .my-4 {
    margin-block: calc(var(--spacing) * 4);
  }
  .my-5 {
    margin-block: calc(var(--spacing) * 5);
  }
  .my-6 {
    margin-block: calc(var(--spacing) * 6);
  }
  .my-8 {
    margin-block: calc(var(--spacing) * 8);
  }
  .my-auto {
    margin-block: auto;
  }
  .-ms-0\.5 {
    margin-inline-start: calc(var(--spacing) * -0.5);
  }
  .-ms-1 {
    margin-inline-start: calc(var(--spacing) * -1);
  }
  .-ms-2 {
    margin-inline-start: calc(var(--spacing) * -2);
  }
  .ms-0\.5 {
    margin-inline-start: calc(var(--spacing) * 0.5);
  }
  .ms-1 {
    margin-inline-start: calc(var(--spacing) * 1);
  }
  .ms-1\.5 {
    margin-inline-start: calc(var(--spacing) * 1.5);
  }
  .ms-2 {
    margin-inline-start: calc(var(--spacing) * 2);
  }
  .ms-3 {
    margin-inline-start: calc(var(--spacing) * 3);
  }
  .ms-4 {
    margin-inline-start: calc(var(--spacing) * 4);
  }
  .ms-5 {
    margin-inline-start: calc(var(--spacing) * 5);
  }
  .ms-6 {
    margin-inline-start: calc(var(--spacing) * 6);
  }
  .ms-auto {
    margin-inline-start: auto;
  }
  .-me-0\.5 {
    margin-inline-end: calc(var(--spacing) * -0.5);
  }
  .-me-1 {
    margin-inline-end: calc(var(--spacing) * -1);
  }
  .me-1 {
    margin-inline-end: calc(var(--spacing) * 1);
  }
  .me-1\.5 {
    margin-inline-end: calc(var(--spacing) * 1.5);
  }
  .me-2 {
    margin-inline-end: calc(var(--spacing) * 2);
  }
  .me-2\.5 {
    margin-inline-end: calc(var(--spacing) * 2.5);
  }
  .me-3 {
    margin-inline-end: calc(var(--spacing) * 3);
  }
  .me-4 {
    margin-inline-end: calc(var(--spacing) * 4);
  }
  .me-6 {
    margin-inline-end: calc(var(--spacing) * 6);
  }
  .me-8 {
    margin-inline-end: calc(var(--spacing) * 8);
  }
  .me-auto {
    margin-inline-end: auto;
  }
  .format-base {
    font-size: 1rem;
    line-height: 1.75;
    :where(p):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
    }
    :where([class~="lead"]):not(:where([class~="not-format"] *)) {
      font-size: 1.25em;
      line-height: 1.6;
      margin-top: 1.2em;
      margin-bottom: 1.2em;
    }
    :where(blockquote):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.6em;
    }
    :where(blockquote):not(:where([class~="not-format"] *))::before {
      margin-top: 1.6em;
    }
    :where(blockquote > p:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h1):not(:where([class~="not-format"] *)) {
      font-size: 2.25em;
      margin-top: 0;
      margin-bottom: 0.8888889em;
      line-height: 1.1111111;
    }
    :where(h2):not(:where([class~="not-format"] *)) {
      font-size: 1.5em;
      margin-top: 0;
      margin-bottom: 1em;
      line-height: 1.3333333;
    }
    :where(h3):not(:where([class~="not-format"] *)) {
      font-size: 1.25em;
      margin-top: 0;
      margin-bottom: 0.6em;
      line-height: 1.6;
    }
    :where(h4):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0.5em;
      line-height: 1.5;
    }
    :where(img):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(video):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(figure):not(:where([class~="not-format"] *)) {
      margin-top: 2em;
      margin-bottom: 2em;
    }
    :where(figure > *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.4285714;
      margin-top: 0.8571429em;
    }
    :where(code):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
    }
    :where(h2 code):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
    }
    :where(h3 code):not(:where([class~="not-format"] *)) {
      font-size: 0.9em;
    }
    :where(pre):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.7142857;
      margin-top: 1.7142857em;
      margin-bottom: 1.7142857em;
      border-radius: 0.375rem;
      padding-top: 0.8571429em;
      padding-right: 1.1428571em;
      padding-bottom: 0.8571429em;
      padding-left: 1.1428571em;
    }
    :where(ol):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-left: 1.625em;
    }
    :where(ul):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
      margin-bottom: 1.25em;
      padding-left: 1.625em;
    }
    :where(li):not(:where([class~="not-format"] *)) {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }
    :where(ol > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(ul > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.375em;
    }
    :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
    }
    :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.25em;
    }
    :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.25em;
    }
    :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.25em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-format"] *)) {
      margin-top: 0.75em;
      margin-bottom: 0.75em;
    }
    :where(hr):not(:where([class~="not-format"] *)) {
      margin-top: 3em;
      margin-bottom: 3em;
    }
    :where(hr + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(table):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
      line-height: 1.7142857;
    }
    :where(thead th):not(:where([class~="not-format"] *)) {
      padding-right: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-left: 0.5714286em;
    }
    :where(thead th:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-format"] *)) {
      padding-top: 0.5714286em;
      padding-right: 0.5714286em;
      padding-bottom: 0.5714286em;
      padding-left: 0.5714286em;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(.format > :first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(.format > :last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 0;
    }
  }
  .format-lg {
    font-size: 1.125rem;
    line-height: 1.7777778;
    :where(p):not(:where([class~="not-format"] *)) {
      margin-top: 1.3333333em;
      margin-bottom: 1.3333333em;
    }
    :where([class~="lead"]):not(:where([class~="not-format"] *)) {
      font-size: 1.2222222em;
      line-height: 1.4545455;
      margin-top: 1.0909091em;
      margin-bottom: 1.0909091em;
    }
    :where(blockquote):not(:where([class~="not-format"] *))::before {
      margin-top: 1.6666667em;
    }
    :where(blockquote > p:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0.5em;
    }
    :where(h1):not(:where([class~="not-format"] *)) {
      font-size: 2.6666667em;
      margin-top: 0;
      margin-bottom: 0.8333333em;
      line-height: 1;
    }
    :where(h2):not(:where([class~="not-format"] *)) {
      font-size: 2em;
      margin-top: 0;
      margin-bottom: 0.6666667em;
      line-height: 1.3333333;
    }
    :where(h3):not(:where([class~="not-format"] *)) {
      font-size: 1.3333333em;
      margin-top: 0;
      margin-bottom: 0.6666667em;
      line-height: 1.5;
    }
    :where(h4):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0.4444444em;
      line-height: 1.5555556;
    }
    :where(img):not(:where([class~="not-format"] *)) {
      margin-top: 1.7777778em;
      margin-bottom: 1.7777778em;
    }
    :where(video):not(:where([class~="not-format"] *)) {
      margin-top: 1.7777778em;
      margin-bottom: 1.7777778em;
    }
    :where(figure):not(:where([class~="not-format"] *)) {
      margin-top: 1.7777778em;
      margin-bottom: 1.7777778em;
    }
    :where(figure > *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
      margin-bottom: 0;
    }
    :where(figcaption):not(:where([class~="not-format"] *)) {
      font-size: 0.8888889em;
      line-height: 1.5;
      margin-top: 1em;
    }
    :where(code):not(:where([class~="not-format"] *)) {
      font-size: 0.8888889em;
    }
    :where(h2 code):not(:where([class~="not-format"] *)) {
      font-size: 0.8666667em;
    }
    :where(h3 code):not(:where([class~="not-format"] *)) {
      font-size: 0.875em;
    }
    :where(pre):not(:where([class~="not-format"] *)) {
      font-size: 0.8888889em;
      line-height: 1.75;
      margin-top: 2em;
      margin-bottom: 2em;
      border-radius: 0.375rem;
      padding-top: 1em;
      padding-right: 1.5em;
      padding-bottom: 1em;
      padding-left: 1.5em;
    }
    :where(ol):not(:where([class~="not-format"] *)) {
      margin-top: 1.3333333em;
      margin-bottom: 1.3333333em;
      padding-left: 1.5555556em;
    }
    :where(ul):not(:where([class~="not-format"] *)) {
      margin-top: 1.3333333em;
      margin-bottom: 1.3333333em;
      padding-left: 1.5555556em;
    }
    :where(li):not(:where([class~="not-format"] *)) {
      margin-top: 0.6666667em;
      margin-bottom: 0.6666667em;
    }
    :where(ol > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.4444444em;
    }
    :where(ul > li):not(:where([class~="not-format"] *)) {
      padding-left: 0.4444444em;
    }
    :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
      margin-top: 0.8888889em;
      margin-bottom: 0.8888889em;
    }
    :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.3333333em;
    }
    :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.3333333em;
    }
    :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
      margin-top: 1.3333333em;
    }
    :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 1.3333333em;
    }
    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-format"] *)) {
      margin-top: 0.8888889em;
      margin-bottom: 0.8888889em;
    }
    :where(hr):not(:where([class~="not-format"] *)) {
      margin-top: 3.1111111em;
      margin-bottom: 3.1111111em;
    }
    :where(hr + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h2 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h3 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(h4 + *):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(table):not(:where([class~="not-format"] *)) {
      font-size: 0.8888889em;
      line-height: 1.5;
    }
    :where(thead th):not(:where([class~="not-format"] *)) {
      padding-right: 0.75em;
      padding-bottom: 0.75em;
      padding-left: 0.75em;
    }
    :where(thead th:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(tbody td, tfoot td):not(:where([class~="not-format"] *)) {
      padding-top: 0.75em;
      padding-right: 0.75em;
      padding-bottom: 0.75em;
      padding-left: 0.75em;
    }
    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-format"] *)) {
      padding-right: 0;
    }
    :where(.format > :first-child):not(:where([class~="not-format"] *)) {
      margin-top: 0;
    }
    :where(.format > :last-child):not(:where([class~="not-format"] *)) {
      margin-bottom: 0;
    }
  }
  .-mt-5 {
    margin-top: calc(var(--spacing) * -5);
  }
  .mt-0 {
    margin-top: calc(var(--spacing) * 0);
  }
  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }
  .mt-1\.5 {
    margin-top: calc(var(--spacing) * 1.5);
  }
  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }
  .mt-2\.5 {
    margin-top: calc(var(--spacing) * 2.5);
  }
  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }
  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }
  .mt-5 {
    margin-top: calc(var(--spacing) * 5);
  }
  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }
  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }
  .apexcharts-canvas {
    & .apexcharts-tooltip {
      background-color: white !important;
      color: var(--color-gray-700) !important;
      border: 0 !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark & .apexcharts-tooltip {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-title {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    & .apexcharts-xaxistooltip {
      color: var(--color-gray-500) !important;
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      border-color: transparent !important;
      background-color: white !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark & .apexcharts-xaxistooltip {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    .dark & .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
    & .apexcharts-xaxistooltip-text {
      font-weight: 400 !important;
      font-size: 0.875rem !important;
    }
    & .apexcharts-xaxistooltip:after {
      border-bottom-color: white !important;
    }
    & .apexcharts-xaxistooltip:before {
      border-bottom-color: white !important;
    }
    & .apexcharts-xaxistooltip:after {
      border-width: 8px !important;
      margin-left: -8px !important;
    }
    & .apexcharts-xaxistooltip:before {
      border-width: 10px !important;
      margin-left: -10px !important;
    }
    .dark & .apexcharts-xaxistooltip:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    .dark & .apexcharts-xaxistooltip:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-y-group {
      padding: 0 !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
      padding-bottom: 0.75rem !important;
      background-color: white !important;
      color: var(--color-gray-500) !important;
    }
    .dark & .apexcharts-tooltip-series-group.apexcharts-active {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-tooltip-series-group.apexcharts-active:first-of-type {
      padding-top: 0.75rem !important;
    }
    & .apexcharts-legend {
      padding: 0 !important;
    }
    & .apexcharts-legend-text {
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      padding-left: 1.25rem !important;
      color: var(--color-gray-500) !important;
    }
    :is([dir=rtl]) & .apexcharts-legend-text {
      padding-right: 0.5rem !important;
    }
    & .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: var(--color-gray-900) !important;
    }
    .dark & .apexcharts-legend-text {
      color: var(--color-gray-400) !important;
    }
    .dark & .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
    & .apexcharts-legend-series {
      margin-left: 0.5rem !important;
      margin-right: 0.5rem !important;
      margin-bottom: 0.25rem !important;
      display: flex !important;
      align-items: center !important;
    }
    .dark & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark & .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
    & .apexcharts-datalabels .apexcharts-text.apexcharts-pie-label {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-legend-series {
    .apexcharts-canvas & {
      margin-left: 0.5rem !important;
      margin-right: 0.5rem !important;
      margin-bottom: 0.25rem !important;
      display: flex !important;
      align-items: center !important;
    }
  }
  .apexcharts-tooltip {
    .apexcharts-canvas & {
      background-color: white !important;
      color: var(--color-gray-700) !important;
      border: 0 !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark .apexcharts-canvas & {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-title {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    .apexcharts-canvas & .apexcharts-tooltip-text-y-value {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    :is([dir=rtl]) & .apexcharts-tooltip-marker {
      margin-right: 0px !important;
      margin-left: e !important;
    }
    .dark .apexcharts-canvas & .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
  }
  .datatable-top {
    .datatable-wrapper & {
      display: flex;
      justify-content: space-between;
      flex-direction: column-reverse;
      align-items: start;
      gap: 1rem;
      margin-bottom: 1rem;
      @media (min-width: 640px) {
        flex-direction: row-reverse;
        align-items: center;
      }
    }
    .datatable-wrapper & .datatable-dropdown {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper & .datatable-dropdown {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper & .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .apexcharts-tooltip-marker {
    :is([dir=rtl]) .apexcharts-tooltip & {
      margin-right: 0px !important;
      margin-left: e !important;
    }
  }
  .datatable-dropdown {
    .datatable-wrapper .datatable-top & {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper .datatable-top & {
      color: var(--color-gray-400);
    }
    .datatable-wrapper .datatable-top & .datatable-selector {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper .datatable-top & .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .datatable-selector {
    .datatable-wrapper .datatable-top .datatable-dropdown & {
      background-color: var(--color-gray-50);
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      margin-right: 0.25rem;
      min-width: 4rem;
    }
    .dark .datatable-wrapper .datatable-top .datatable-dropdown & {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
  }
  .\!mr-0 {
    margin-right: calc(var(--spacing) * 0) !important;
  }
  .\!mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5) !important;
  }
  .\!mr-2 {
    margin-right: calc(var(--spacing) * 2) !important;
  }
  .-mr-1 {
    margin-right: calc(var(--spacing) * -1);
  }
  .-mr-3 {
    margin-right: calc(var(--spacing) * -3);
  }
  .mr-0 {
    margin-right: calc(var(--spacing) * 0);
  }
  .mr-0\.5 {
    margin-right: calc(var(--spacing) * 0.5);
  }
  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }
  .mr-1\.5 {
    margin-right: calc(var(--spacing) * 1.5);
  }
  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }
  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }
  .mr-4 {
    margin-right: calc(var(--spacing) * 4);
  }
  .mr-auto {
    margin-right: auto;
  }
  .apexcharts-tooltip-title {
    .apexcharts-canvas .apexcharts-tooltip & {
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      margin-bottom: 0.75rem !important;
      background-color: var(--color-gray-100) !important;
      border-bottom-color: var(--color-gray-200) !important;
      font-size: 0.875rem !important;
      font-weight: 400 !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
  }
  .\!mb-0 {
    margin-bottom: calc(var(--spacing) * 0) !important;
  }
  .-mb-px {
    margin-bottom: -1px;
  }
  .mb-0 {
    margin-bottom: calc(var(--spacing) * 0);
  }
  .mb-0\.5 {
    margin-bottom: calc(var(--spacing) * 0.5);
  }
  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }
  .mb-1\.5 {
    margin-bottom: calc(var(--spacing) * 1.5);
  }
  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }
  .mb-2\.5 {
    margin-bottom: calc(var(--spacing) * 2.5);
  }
  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }
  .mb-3\.5 {
    margin-bottom: calc(var(--spacing) * 3.5);
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-5 {
    margin-bottom: calc(var(--spacing) * 5);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }
  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }
  .apexcharts-xaxistooltip {
    .apexcharts-canvas & {
      color: var(--color-gray-500) !important;
      padding-top: 0.5rem !important;
      padding-bottom: 0.5rem !important;
      padding-right: 0.75rem !important;
      padding-left: 0.75rem !important;
      border-color: transparent !important;
      background-color: white !important;
      border-radius: 0.25rem !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    .dark .apexcharts-canvas & {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    .apexcharts-canvas &:after {
      border-bottom-color: white !important;
    }
    .apexcharts-canvas &:before {
      border-bottom-color: white !important;
    }
    .apexcharts-canvas &:after {
      border-width: 8px !important;
      margin-left: -8px !important;
    }
    .apexcharts-canvas &:before {
      border-width: 10px !important;
      margin-left: -10px !important;
    }
    .dark .apexcharts-canvas &:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    .dark .apexcharts-canvas &:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
  }
  .\!ml-0 {
    margin-left: calc(var(--spacing) * 0) !important;
  }
  .\!ml-4 {
    margin-left: calc(var(--spacing) * 4) !important;
  }
  .-ml-0\.5 {
    margin-left: calc(var(--spacing) * -0.5);
  }
  .-ml-1 {
    margin-left: calc(var(--spacing) * -1);
  }
  .ml-0 {
    margin-left: calc(var(--spacing) * 0);
  }
  .ml-1 {
    margin-left: calc(var(--spacing) * 1);
  }
  .ml-1\.5 {
    margin-left: calc(var(--spacing) * 1.5);
  }
  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }
  .ml-3 {
    margin-left: calc(var(--spacing) * 3);
  }
  .ml-4 {
    margin-left: calc(var(--spacing) * 4);
  }
  .ml-6 {
    margin-left: calc(var(--spacing) * 6);
  }
  .ml-auto {
    margin-left: auto;
  }
  .datatable-pagination-list {
    .datatable-wrapper .datatable-bottom .datatable-pagination & {
      display: flex;
      align-items: center;
      height: 2rem;
      font-size: 0.875rem;
    }
  }
  .\!flex {
    display: flex !important;
  }
  .\!inline-block {
    display: inline-block !important;
  }
  .\!inline-flex {
    display: inline-flex !important;
  }
  .block {
    display: block;
  }
  .contents {
    display: contents;
  }
  .flex {
    display: flex;
  }
  .flow-root {
    display: flow-root;
  }
  .grid {
    display: grid;
  }
  .hidden {
    display: none;
  }
  .inline {
    display: inline;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .list-item {
    display: list-item;
  }
  .table {
    display: table;
  }
  .table-column {
    display: table-column;
  }
  .aspect-square {
    aspect-ratio: 1 / 1;
  }
  .\!h-0 {
    height: calc(var(--spacing) * 0) !important;
  }
  .\!h-2 {
    height: calc(var(--spacing) * 2) !important;
  }
  .\!h-4 {
    height: calc(var(--spacing) * 4) !important;
  }
  .h-0 {
    height: calc(var(--spacing) * 0);
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-2\.5 {
    height: calc(var(--spacing) * 2.5);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-11 {
    height: calc(var(--spacing) * 11);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-14 {
    height: calc(var(--spacing) * 14);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-20 {
    height: calc(var(--spacing) * 20);
  }
  .h-24 {
    height: calc(var(--spacing) * 24);
  }
  .h-25 {
    height: calc(var(--spacing) * 25);
  }
  .h-32 {
    height: calc(var(--spacing) * 32);
  }
  .h-36 {
    height: calc(var(--spacing) * 36);
  }
  .h-48 {
    height: calc(var(--spacing) * 48);
  }
  .h-56 {
    height: calc(var(--spacing) * 56);
  }
  .h-60 {
    height: calc(var(--spacing) * 60);
  }
  .h-64 {
    height: calc(var(--spacing) * 64);
  }
  .h-\[78px\] {
    height: 78px;
  }
  .h-\[calc\(100\%-1rem\)\] {
    height: calc(100% - 1rem);
  }
  .h-\[calc\(100vh-4rem\)\] {
    height: calc(100vh - 4rem);
  }
  .h-\[calc\(100vh-7rem\)\] {
    height: calc(100vh - 7rem);
  }
  .h-\[calc\(100vh-8rem\)\] {
    height: calc(100vh - 8rem);
  }
  .h-\[calc\(100vh-13rem\)\] {
    height: calc(100vh - 13rem);
  }
  .h-\[calc\(100vh-15\.8rem\)\] {
    height: calc(100vh - 15.8rem);
  }
  .h-\[calc\(100vh-16rem\)\] {
    height: calc(100vh - 16rem);
  }
  .h-\[calc\(100vh-18\.5rem\)\] {
    height: calc(100vh - 18.5rem);
  }
  .h-\[calc\(100vh-18\.7rem\)\] {
    height: calc(100vh - 18.7rem);
  }
  .h-\[calc\(100vh-19\.4rem\)\] {
    height: calc(100vh - 19.4rem);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-screen {
    height: 100vh;
  }
  .h-svh {
    height: 100svh;
  }
  .max-h-48 {
    max-height: calc(var(--spacing) * 48);
  }
  .max-h-96 {
    max-height: calc(var(--spacing) * 96);
  }
  .max-h-full {
    max-height: 100%;
  }
  .min-h-25 {
    min-height: calc(var(--spacing) * 25);
  }
  .datatable-table {
    .datatable-wrapper & {
      width: 100%;
      font-size: 0.875rem;
      color: var(--color-gray-500);
      text-align: left;
    }
    .dark .datatable-wrapper & {
      color: var(--color-gray-400);
    }
    .datatable-wrapper & thead {
      font-size: 0.75rem;
      color: var(--color-gray-500);
      background-color: var(--color-gray-50);
    }
    .dark .datatable-wrapper & thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    .datatable-wrapper & thead th {
      white-space: nowrap;
    }
    .datatable-wrapper & thead th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & tbody th {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & tbody td {
      width: auto !important;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
    .datatable-wrapper & thead th .datatable-sorter {
      text-transform: uppercase;
    }
    .datatable-wrapper & thead th {
      text-transform: uppercase;
    }
    .datatable-wrapper & thead th .datatable-sorter:hover {
      color: var(--color-gray-900);
    }
    .datatable-wrapper & thead th.datatable-ascending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .datatable-wrapper & thead th.datatable-descending .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper & thead th .datatable-sorter:hover {
      color: white;
    }
    .dark .datatable-wrapper & thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    .dark .datatable-wrapper & thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    .datatable-wrapper & tbody tr.selected {
      background-color: var(--color-gray-100);
    }
    .dark .datatable-wrapper & tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    .datatable-wrapper & tbody tr {
      border-bottom: 1px solid var(--color-gray-200);
    }
    .dark .datatable-wrapper & tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-empty {
      text-align: center;
    }
  }
  .\!w-2 {
    width: calc(var(--spacing) * 2) !important;
  }
  .\!w-full {
    width: 100% !important;
  }
  .w-1\.5 {
    width: calc(var(--spacing) * 1.5);
  }
  .w-1\/2 {
    width: calc(1/2 * 100%);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-2\.5 {
    width: calc(var(--spacing) * 2.5);
  }
  .w-2\/5 {
    width: calc(2/5 * 100%);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-11 {
    width: calc(var(--spacing) * 11);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-14 {
    width: calc(var(--spacing) * 14);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-24 {
    width: calc(var(--spacing) * 24);
  }
  .w-28 {
    width: calc(var(--spacing) * 28);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-36 {
    width: calc(var(--spacing) * 36);
  }
  .w-40 {
    width: calc(var(--spacing) * 40);
  }
  .w-44 {
    width: calc(var(--spacing) * 44);
  }
  .w-48 {
    width: calc(var(--spacing) * 48);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-56 {
    width: calc(var(--spacing) * 56);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-72 {
    width: calc(var(--spacing) * 72);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-\[106px\] {
    width: 106px;
  }
  .w-\[145px\] {
    width: 145px;
  }
  .w-\[340px\] {
    width: 340px;
  }
  .w-\[356px\] {
    width: 356px;
  }
  .w-\[360px\] {
    width: 360px;
  }
  .w-\[512px\] {
    width: 512px;
  }
  .w-\[calc\(100\%-2rem\)\] {
    width: calc(100% - 2rem);
  }
  .w-auto {
    width: auto;
  }
  .w-fit {
    width: fit-content;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .\!max-w-12 {
    max-width: calc(var(--spacing) * 12) !important;
  }
  .max-w-2xl {
    max-width: var(--container-2xl);
  }
  .max-w-3xl {
    max-width: var(--container-3xl);
  }
  .max-w-4xl {
    max-width: var(--container-4xl);
  }
  .max-w-5xl {
    max-width: var(--container-5xl);
  }
  .max-w-6xl {
    max-width: var(--container-6xl);
  }
  .max-w-7xl {
    max-width: var(--container-7xl);
  }
  .max-w-12 {
    max-width: calc(var(--spacing) * 12);
  }
  .max-w-48 {
    max-width: calc(var(--spacing) * 48);
  }
  .max-w-52 {
    max-width: calc(var(--spacing) * 52);
  }
  .max-w-64 {
    max-width: calc(var(--spacing) * 64);
  }
  .max-w-80 {
    max-width: calc(var(--spacing) * 80);
  }
  .max-w-\[2\.5rem\] {
    max-width: 2.5rem;
  }
  .max-w-\[14rem\] {
    max-width: 14rem;
  }
  .max-w-\[320px\] {
    max-width: 320px;
  }
  .max-w-\[404px\] {
    max-width: 404px;
  }
  .max-w-full {
    max-width: 100%;
  }
  .max-w-lg {
    max-width: var(--container-lg);
  }
  .max-w-md {
    max-width: var(--container-md);
  }
  .max-w-none {
    max-width: none;
  }
  .max-w-screen-2xl {
    max-width: var(--breakpoint-2xl);
  }
  .max-w-screen-lg {
    max-width: var(--breakpoint-lg);
  }
  .max-w-screen-md {
    max-width: var(--breakpoint-md);
  }
  .max-w-screen-xl {
    max-width: var(--breakpoint-xl);
  }
  .max-w-sm {
    max-width: var(--container-sm);
  }
  .max-w-xl {
    max-width: var(--container-xl);
  }
  .max-w-xs {
    max-width: var(--container-xs);
  }
  .datatable-input {
    .datatable-wrapper .datatable-search & {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .datatable-wrapper & {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark .datatable-wrapper .datatable-search & {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .dark .datatable-wrapper & {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper thead th & {
      background-color: white;
      font-weight: 400;
      color: var(--color-gray-900);
      padding-top: .35rem;
      padding-bottom: .35rem;
      min-width: 0;
    }
    .dark .datatable-wrapper thead th & {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    .datatable-wrapper .datatable-search &:focus {
      border-color: var(--color-blue-600);
    }
  }
  .datatable-search {
    .datatable-wrapper & .datatable-input {
      color: var(--color-gray-900);
      font-size: 0.875rem;
      border: 1px solid var(--color-gray-300);
      border-radius: 0.5rem;
      background-color: var(--color-gray-50);
      min-width: 16rem;
    }
    .dark .datatable-wrapper & .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    .datatable-wrapper & .datatable-input:focus {
      border-color: var(--color-blue-600);
    }
  }
  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }
  .min-w-24 {
    min-width: calc(var(--spacing) * 24);
  }
  .min-w-28 {
    min-width: calc(var(--spacing) * 28);
  }
  .min-w-32 {
    min-width: calc(var(--spacing) * 32);
  }
  .min-w-40 {
    min-width: calc(var(--spacing) * 40);
  }
  .min-w-48 {
    min-width: calc(var(--spacing) * 48);
  }
  .min-w-56 {
    min-width: calc(var(--spacing) * 56);
  }
  .min-w-64 {
    min-width: calc(var(--spacing) * 64);
  }
  .min-w-80 {
    min-width: calc(var(--spacing) * 80);
  }
  .min-w-\[12rem\] {
    min-width: 12rem;
  }
  .min-w-\[14rem\] {
    min-width: 14rem;
  }
  .min-w-\[28rem\] {
    min-width: 28rem;
  }
  .min-w-full {
    min-width: 100%;
  }
  .flex-1 {
    flex: 1;
  }
  .flex-none {
    flex: none;
  }
  .\!shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-shrink {
    flex-shrink: 1;
  }
  .flex-shrink-0 {
    flex-shrink: 0;
  }
  .shrink {
    flex-shrink: 1;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .flex-grow {
    flex-grow: 1;
  }
  .grow {
    flex-grow: 1;
  }
  .table-fixed {
    table-layout: fixed;
  }
  .border-collapse {
    border-collapse: collapse;
  }
  .origin-\[0\] {
    transform-origin: 0;
  }
  .-translate-x-1\/2 {
    --tw-translate-x: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-x-full {
    --tw-translate-x: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-0 {
    --tw-translate-x: calc(var(--spacing) * 0);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-x-full {
    --tw-translate-x: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-1\/2 {
    --tw-translate-y: calc(calc(1/2 * 100%) * -1);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-6 {
    --tw-translate-y: calc(var(--spacing) * -6);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .-translate-y-full {
    --tw-translate-y: -100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .translate-y-full {
    --tw-translate-y: 100%;
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }
  .scale-75 {
    --tw-scale-x: 75%;
    --tw-scale-y: 75%;
    --tw-scale-z: 75%;
    scale: var(--tw-scale-x) var(--tw-scale-y);
  }
  .rotate-90 {
    rotate: 90deg;
  }
  .rotate-180 {
    rotate: 180deg;
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .transform-none {
    transform: none;
  }
  .\!cursor-none {
    cursor: none !important;
  }
  .\!cursor-pointer {
    cursor: pointer !important;
  }
  .cursor-default {
    cursor: default;
  }
  .cursor-move {
    cursor: move;
  }
  .cursor-none {
    cursor: none;
  }
  .cursor-not-allowed {
    cursor: not-allowed;
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .resize {
    resize: both;
  }
  .list-inside {
    list-style-position: inside;
  }
  .list-decimal {
    list-style-type: decimal;
  }
  .list-none {
    list-style-type: none;
  }
  .\[appearance\:textfield\] {
    appearance: textfield;
  }
  .appearance-none {
    appearance: none;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  .grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  .grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
  .grid-cols-7 {
    grid-template-columns: repeat(7, minmax(0, 1fr));
  }
  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
  .\!flex-col {
    flex-direction: column !important;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-col-reverse {
    flex-direction: column-reverse;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .place-items-center {
    place-items: center;
  }
  .content-center {
    align-content: center;
  }
  .\!items-center {
    align-items: center !important;
  }
  .items-baseline {
    align-items: baseline;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .items-stretch {
    align-items: stretch;
  }
  .\!justify-center {
    justify-content: center !important;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .justify-start {
    justify-content: flex-start;
  }
  .justify-items-center {
    justify-items: center;
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-1\.5 {
    gap: calc(var(--spacing) * 1.5);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-2\.5 {
    gap: calc(var(--spacing) * 2.5);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-5 {
    gap: calc(var(--spacing) * 5);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }
  .\!space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .\!space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  .space-y-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-2 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-3 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-4 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-5 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .space-y-6 {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0;
      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
    }
  }
  .gap-x-4 {
    column-gap: calc(var(--spacing) * 4);
  }
  .gap-x-6 {
    column-gap: calc(var(--spacing) * 6);
  }
  .-space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * -4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * -4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .-space-x-px {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(-1px * var(--tw-space-x-reverse));
      margin-inline-end: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-0 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-0\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-1\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 1.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 1.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-2\.5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 2.5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 2.5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-3 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-4 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-5 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-6 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .space-x-8 {
    :where(& > :not(:last-child)) {
      --tw-space-x-reverse: 0;
      margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
      margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
    }
  }
  .gap-y-4 {
    row-gap: calc(var(--spacing) * 4);
  }
  .gap-y-8 {
    row-gap: calc(var(--spacing) * 8);
  }
  .divide-x {
    :where(& > :not(:last-child)) {
      --tw-divide-x-reverse: 0;
      border-inline-style: var(--tw-border-style);
      border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
      border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
    }
  }
  .divide-y {
    :where(& > :not(:last-child)) {
      --tw-divide-y-reverse: 0;
      border-bottom-style: var(--tw-border-style);
      border-top-style: var(--tw-border-style);
      border-top-width: calc(1px * var(--tw-divide-y-reverse));
      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    }
  }
  .divide-gray-100 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-100);
    }
  }
  .divide-gray-200 {
    :where(& > :not(:last-child)) {
      border-color: var(--color-gray-200);
    }
  }
  .place-self-center {
    place-self: center;
  }
  .self-center {
    align-self: center;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-scroll {
    overflow: scroll;
  }
  .datatable-container {
    .datatable-wrapper & thead tr.search-filtering-row th {
      padding-top: 0;
    }
    .datatable-wrapper & {
      overflow-x: auto;
    }
  }
  .overflow-x-auto {
    overflow-x: auto;
  }
  .overflow-x-hidden {
    overflow-x: hidden;
  }
  .overflow-x-scroll {
    overflow-x: scroll;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .overflow-y-hidden {
    overflow-y: hidden;
  }
  .overflow-y-scroll {
    overflow-y: scroll;
  }
  .\!rounded-full {
    border-radius: calc(infinity * 1px) !important;
  }
  .\!rounded-lg {
    border-radius: var(--radius-lg) !important;
  }
  .\!rounded-sm {
    border-radius: var(--radius-sm) !important;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-none {
    border-radius: 0;
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-xs {
    border-radius: var(--radius-xs);
  }
  .rounded-s-lg {
    border-start-start-radius: var(--radius-lg);
    border-end-start-radius: var(--radius-lg);
  }
  .rounded-s-xl {
    border-start-start-radius: var(--radius-xl);
    border-end-start-radius: var(--radius-xl);
  }
  .rounded-e-lg {
    border-start-end-radius: var(--radius-lg);
    border-end-end-radius: var(--radius-lg);
  }
  .rounded-e-xl {
    border-start-end-radius: var(--radius-xl);
    border-end-end-radius: var(--radius-xl);
  }
  .rounded-ee-xl {
    border-end-end-radius: var(--radius-xl);
  }
  .rounded-es-xl {
    border-end-start-radius: var(--radius-xl);
  }
  .rounded-t {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-l-md {
    border-top-left-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .rounded-r-md {
    border-top-right-radius: var(--radius-md);
    border-bottom-right-radius: var(--radius-md);
  }
  .rounded-b {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .\!border {
    border-style: var(--tw-border-style) !important;
    border-width: 1px !important;
  }
  .\!border-0 {
    border-style: var(--tw-border-style) !important;
    border-width: 0px !important;
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border\! {
    border-style: var(--tw-border-style) !important;
    border-width: 1px !important;
  }
  .border-0 {
    border-style: var(--tw-border-style);
    border-width: 0px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-x {
    border-inline-style: var(--tw-border-style);
    border-inline-width: 1px;
  }
  .border-y {
    border-block-style: var(--tw-border-style);
    border-block-width: 1px;
  }
  .border-s {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 1px;
  }
  .border-s-2 {
    border-inline-start-style: var(--tw-border-style);
    border-inline-start-width: 2px;
  }
  .border-e {
    border-inline-end-style: var(--tw-border-style);
    border-inline-end-width: 1px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-r-0 {
    border-right-style: var(--tw-border-style);
    border-right-width: 0px;
  }
  .\!border-b {
    border-bottom-style: var(--tw-border-style) !important;
    border-bottom-width: 1px !important;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .\!border-l-0 {
    border-left-style: var(--tw-border-style) !important;
    border-left-width: 0px !important;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-l-0 {
    border-left-style: var(--tw-border-style);
    border-left-width: 0px;
  }
  .\!border-solid {
    --tw-border-style: solid !important;
    border-style: solid !important;
  }
  .border-dashed {
    --tw-border-style: dashed;
    border-style: dashed;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .dark {
    & .apexcharts-canvas .apexcharts-tooltip {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
      border-color: transparent !important;
      box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
      background-color: var(--color-gray-600) !important;
      border-color: var(--color-gray-500) !important;
      color: var(--color-gray-500) !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip {
      color: var(--color-gray-400) !important;
      background-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
      color: white !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip:after {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-xaxistooltip:before {
      border-bottom-color: var(--color-gray-700) !important;
    }
    & .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-legend-text {
      color: var(--color-gray-400) !important;
    }
    & .apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
    & .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    & .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
    & .apexcharts-gridline {
      stroke: var(--color-gray-700) !important;
    }
    & .apexcharts-xcrosshairs {
      stroke: var(--color-gray-700) !important;
    }
    & .apexcharts-ycrosshairs {
      stroke: var(--color-gray-700) !important;
    }
  }
  .dark {
    & .datatable-wrapper .datatable-search .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-input {
      color: white;
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper thead th .datatable-input {
      background-color: var(--color-gray-700);
      border-color: var(--color-gray-600);
      color: white;
    }
    & .datatable-wrapper .datatable-top .datatable-dropdown {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-top .datatable-dropdown .datatable-selector {
      background-color: var(--color-gray-800);
      border: 1px solid var(--color-gray-700);
      color: white;
    }
    & .datatable-wrapper .datatable-table {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-table thead {
      color: var(--color-gray-400);
      background-color: var(--color-gray-800);
    }
    & .datatable-wrapper .datatable-table thead th .datatable-sorter:hover {
      color: white;
    }
    & .datatable-wrapper .datatable-table thead th.datatable-ascending .datatable-sorter {
      color: white;
    }
    & .datatable-wrapper .datatable-table thead th.datatable-descending .datatable-sorter {
      color: white;
    }
    & .datatable-wrapper .datatable-table tbody tr.selected {
      background-color: var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-table tbody tr {
      border-bottom: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-info {
      color: var(--color-gray-400);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
      color: var(--color-gray-400);
      border-color: var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
      color: transparent;
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
      content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
      border-left: 1px solid var(--color-gray-700);
    }
    & .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
      background-color: var(--color-gray-700);
      color: white;
    }
  }
  .\!border-gray-100 {
    border-color: var(--color-gray-100) !important;
  }
  .\!border-gray-200 {
    border-color: var(--color-gray-200) !important;
  }
  .\!border-gray-300 {
    border-color: var(--color-gray-300) !important;
  }
  .\!border-primary-700 {
    border-color: var(--color-primary-700) !important;
  }
  .border-blue-600 {
    border-color: var(--color-blue-600);
  }
  .border-blue-700 {
    border-color: var(--color-blue-700);
  }
  .border-gray-50 {
    border-color: var(--color-gray-50);
  }
  .border-gray-100 {
    border-color: var(--color-gray-100);
  }
  .border-gray-200 {
    border-color: var(--color-gray-200);
  }
  .border-gray-300 {
    border-color: var(--color-gray-300);
  }
  .border-green-300 {
    border-color: var(--color-green-300);
  }
  .border-orange-200 {
    border-color: var(--color-orange-200);
  }
  .border-primary-300 {
    border-color: var(--color-primary-300);
  }
  .border-primary-600 {
    border-color: var(--color-primary-600);
  }
  .border-primary-700 {
    border-color: var(--color-primary-700);
  }
  .border-red-600 {
    border-color: var(--color-red-600);
  }
  .border-red-700 {
    border-color: var(--color-red-700);
  }
  .border-transparent {
    border-color: transparent;
  }
  .border-white {
    border-color: var(--color-white);
  }
  .border-s-gray-50 {
    border-inline-start-color: var(--color-gray-50);
  }
  .border-s-gray-100 {
    border-inline-start-color: var(--color-gray-100);
  }
  .apexcharts-active {
    .apexcharts-canvas .apexcharts-tooltip-series-group& .apexcharts-tooltip-y-group {
      padding: 0 !important;
    }
    .apexcharts-canvas .apexcharts-tooltip-series-group& {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
      padding-bottom: 0.75rem !important;
      background-color: white !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip-series-group& {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    .apexcharts-canvas .apexcharts-tooltip-series-group&:first-of-type {
      padding-top: 0.75rem !important;
    }
  }
  .apexcharts-tooltip-series-group {
    .apexcharts-canvas &.apexcharts-active .apexcharts-tooltip-y-group {
      padding: 0 !important;
    }
    .apexcharts-canvas &.apexcharts-active {
      padding-left: 0.75rem !important;
      padding-right: 0.75rem !important;
      padding-bottom: 0.75rem !important;
      background-color: white !important;
      color: var(--color-gray-500) !important;
    }
    .dark .apexcharts-canvas &.apexcharts-active {
      background-color: var(--color-gray-700) !important;
      color: var(--color-gray-400) !important;
    }
    .apexcharts-canvas &.apexcharts-active:first-of-type {
      padding-top: 0.75rem !important;
    }
  }
  .selected {
    .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-100);
    }
    .dark .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-700);
    }
  }
  .selected\! {
    .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-100) !important;
    }
    .dark .datatable-wrapper .datatable-table tbody tr& {
      background-color: var(--color-gray-700) !important;
    }
  }
  .selectedCell {
    background-color: var(--color-gray-50);
    .dark & {
      background-color: var(--color-gray-700);
    }
  }
  .\!bg-gray-50 {
    background-color: var(--color-gray-50) !important;
  }
  .\!bg-gray-100\/40 {
    background-color: color-mix(in srgb, #F3F4F6 40%, transparent) !important;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-100) 40%, transparent) !important;
    }
  }
  .\!bg-green-50 {
    background-color: var(--color-green-50) !important;
  }
  .\!bg-green-700 {
    background-color: var(--color-green-700) !important;
  }
  .\!bg-indigo-50 {
    background-color: var(--color-indigo-50) !important;
  }
  .\!bg-indigo-700 {
    background-color: var(--color-indigo-700) !important;
  }
  .\!bg-orange-50 {
    background-color: var(--color-orange-50) !important;
  }
  .\!bg-orange-700 {
    background-color: var(--color-orange-700) !important;
  }
  .\!bg-pink-50 {
    background-color: var(--color-pink-50) !important;
  }
  .\!bg-pink-700 {
    background-color: var(--color-pink-700) !important;
  }
  .\!bg-primary-50 {
    background-color: var(--color-primary-50) !important;
  }
  .\!bg-primary-600 {
    background-color: var(--color-primary-600) !important;
  }
  .\!bg-primary-700 {
    background-color: var(--color-primary-700) !important;
  }
  .\!bg-purple-50 {
    background-color: var(--color-purple-50) !important;
  }
  .\!bg-purple-700 {
    background-color: var(--color-purple-700) !important;
  }
  .\!bg-red-50 {
    background-color: var(--color-red-50) !important;
  }
  .\!bg-red-700 {
    background-color: var(--color-red-700) !important;
  }
  .\!bg-teal-50 {
    background-color: var(--color-teal-50) !important;
  }
  .\!bg-teal-700 {
    background-color: var(--color-teal-700) !important;
  }
  .\!bg-white {
    background-color: var(--color-white) !important;
  }
  .\!bg-yellow-50 {
    background-color: var(--color-yellow-50) !important;
  }
  .\!bg-yellow-700 {
    background-color: var(--color-yellow-700) !important;
  }
  .bg-\[\#4285F4\] {
    background-color: #4285F4;
  }
  .bg-blue-700 {
    background-color: var(--color-blue-700);
  }
  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }
  .bg-gray-100 {
    background-color: var(--color-gray-100);
  }
  .bg-gray-100\/40 {
    background-color: color-mix(in srgb, #F3F4F6 40%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-100) 40%, transparent);
    }
  }
  .bg-gray-200 {
    background-color: var(--color-gray-200);
  }
  .bg-gray-300 {
    background-color: var(--color-gray-300);
  }
  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }
  .bg-gray-600 {
    background-color: var(--color-gray-600);
  }
  .bg-gray-700 {
    background-color: var(--color-gray-700);
  }
  .bg-gray-800 {
    background-color: var(--color-gray-800);
  }
  .bg-gray-900 {
    background-color: var(--color-gray-900);
  }
  .bg-gray-900\/50 {
    background-color: color-mix(in srgb, #111827 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-900) 50%, transparent);
    }
  }
  .bg-green-50 {
    background-color: var(--color-green-50);
  }
  .bg-green-100 {
    background-color: var(--color-green-100);
  }
  .bg-green-300 {
    background-color: var(--color-green-300);
  }
  .bg-green-400 {
    background-color: var(--color-green-400);
  }
  .bg-green-500 {
    background-color: var(--color-green-500);
  }
  .bg-green-700 {
    background-color: var(--color-green-700);
  }
  .bg-indigo-50 {
    background-color: var(--color-indigo-50);
  }
  .bg-indigo-100 {
    background-color: var(--color-indigo-100);
  }
  .bg-indigo-400 {
    background-color: var(--color-indigo-400);
  }
  .bg-indigo-500 {
    background-color: var(--color-indigo-500);
  }
  .bg-indigo-600 {
    background-color: var(--color-indigo-600);
  }
  .bg-indigo-700 {
    background-color: var(--color-indigo-700);
  }
  .bg-orange-50 {
    background-color: var(--color-orange-50);
  }
  .bg-orange-100 {
    background-color: var(--color-orange-100);
  }
  .bg-orange-300 {
    background-color: var(--color-orange-300);
  }
  .bg-orange-400 {
    background-color: var(--color-orange-400);
  }
  .bg-orange-500 {
    background-color: var(--color-orange-500);
  }
  .bg-orange-700 {
    background-color: var(--color-orange-700);
  }
  .bg-pink-50 {
    background-color: var(--color-pink-50);
  }
  .bg-pink-100 {
    background-color: var(--color-pink-100);
  }
  .bg-pink-400 {
    background-color: var(--color-pink-400);
  }
  .bg-pink-500 {
    background-color: var(--color-pink-500);
  }
  .bg-pink-600 {
    background-color: var(--color-pink-600);
  }
  .bg-pink-700 {
    background-color: var(--color-pink-700);
  }
  .bg-primary-50 {
    background-color: var(--color-primary-50);
  }
  .bg-primary-100 {
    background-color: var(--color-primary-100);
  }
  .bg-primary-500 {
    background-color: var(--color-primary-500);
  }
  .bg-primary-600 {
    background-color: var(--color-primary-600);
  }
  .bg-primary-700 {
    background-color: var(--color-primary-700);
  }
  .bg-purple-50 {
    background-color: var(--color-purple-50);
  }
  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }
  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }
  .bg-purple-600 {
    background-color: var(--color-purple-600);
  }
  .bg-purple-700 {
    background-color: var(--color-purple-700);
  }
  .bg-red-50 {
    background-color: var(--color-red-50);
  }
  .bg-red-100 {
    background-color: var(--color-red-100);
  }
  .bg-red-500 {
    background-color: var(--color-red-500);
  }
  .bg-red-600 {
    background-color: var(--color-red-600);
  }
  .bg-red-700 {
    background-color: var(--color-red-700);
  }
  .bg-teal-50 {
    background-color: var(--color-teal-50);
  }
  .bg-teal-100 {
    background-color: var(--color-teal-100);
  }
  .bg-teal-300 {
    background-color: var(--color-teal-300);
  }
  .bg-teal-400 {
    background-color: var(--color-teal-400);
  }
  .bg-teal-600 {
    background-color: var(--color-teal-600);
  }
  .bg-teal-700 {
    background-color: var(--color-teal-700);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .bg-white\/30 {
    background-color: color-mix(in srgb, #ffffff 30%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);
    }
  }
  .bg-white\/50 {
    background-color: color-mix(in srgb, #ffffff 50%, transparent);
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
    }
  }
  .bg-yellow-50 {
    background-color: var(--color-yellow-50);
  }
  .bg-yellow-100 {
    background-color: var(--color-yellow-100);
  }
  .bg-yellow-300 {
    background-color: var(--color-yellow-300);
  }
  .bg-yellow-700 {
    background-color: var(--color-yellow-700);
  }
  .dark {
    & .selectedCell {
      background-color: var(--color-gray-700);
    }
  }
  .bg-\[url\(\'https\:\/\/flowbite\.s3\.amazonaws\.com\/blocks\/e-commerce\/gaming-image\.jpg\'\)\] {
    background-image: url('https://flowbite.s3.amazonaws.com/blocks/e-commerce/gaming-image.jpg');
  }
  .bg-contain {
    background-size: contain;
  }
  .bg-cover {
    background-size: cover;
  }
  .bg-center {
    background-position: center;
  }
  .bg-no-repeat {
    background-repeat: no-repeat;
  }
  .apexcharts-text {
    .apexcharts-datalabels-group &.apexcharts-datalabel-value {
      fill: var(--color-gray-900) !important;
      font-size: 1.875rem,[object Object] !important;
      font-weight: 700 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group &.apexcharts-datalabel-value {
      fill: white !important;
    }
    .apexcharts-canvas .apexcharts-datalabels-group &.apexcharts-datalabel-label {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group &.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
    .apexcharts-canvas .apexcharts-datalabels &.apexcharts-pie-label {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-datalabels-group {
    & .apexcharts-text.apexcharts-datalabel-value {
      fill: var(--color-gray-900) !important;
      font-size: 1.875rem,[object Object] !important;
      font-weight: 700 !important;
    }
    .dark .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-value {
      fill: white !important;
    }
    .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark .apexcharts-canvas & .apexcharts-text.apexcharts-datalabel-label {
      fill: var(--color-gray-400) !important;
    }
  }
  .apexcharts-datalabel-label {
    .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-500) !important;
      font-size: 1rem,[object Object] !important;
      font-weight: 400 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-400) !important;
    }
  }
  .apexcharts-datalabel-value {
    .apexcharts-datalabels-group .apexcharts-text& {
      fill: var(--color-gray-900) !important;
      font-size: 1.875rem,[object Object] !important;
      font-weight: 700 !important;
    }
    .dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text& {
      fill: white !important;
    }
  }
  .fill-gray-500 {
    fill: var(--color-gray-500);
  }
  .apexcharts-gridline {
    stroke: var(--color-gray-200) !important;
    .dark & {
      stroke: var(--color-gray-700) !important;
    }
  }
  .apexcharts-xcrosshairs {
    stroke: var(--color-gray-200) !important;
    .dark & {
      stroke: var(--color-gray-700) !important;
    }
  }
  .apexcharts-ycrosshairs {
    stroke: var(--color-gray-200) !important;
    .dark & {
      stroke: var(--color-gray-700) !important;
    }
  }
  .object-contain {
    object-fit: contain;
  }
  .\!p-0 {
    padding: calc(var(--spacing) * 0) !important;
  }
  .\!p-2 {
    padding: calc(var(--spacing) * 2) !important;
  }
  .apexcharts-legend {
    .apexcharts-canvas & {
      padding: 0 !important;
    }
  }
  .apexcharts-tooltip-y-group {
    .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active & {
      padding: 0 !important;
    }
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-1 {
    padding: calc(var(--spacing) * 1);
  }
  .p-1\.5 {
    padding: calc(var(--spacing) * 1.5);
  }
  .p-2 {
    padding: calc(var(--spacing) * 2);
  }
  .p-2\.5 {
    padding: calc(var(--spacing) * 2.5);
  }
  .p-3 {
    padding: calc(var(--spacing) * 3);
  }
  .p-3\.5 {
    padding: calc(var(--spacing) * 3.5);
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-5 {
    padding: calc(var(--spacing) * 5);
  }
  .p-6 {
    padding: calc(var(--spacing) * 6);
  }
  .p-px {
    padding: 1px;
  }
  .\!px-2 {
    padding-inline: calc(var(--spacing) * 2) !important;
  }
  .\!px-3 {
    padding-inline: calc(var(--spacing) * 3) !important;
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-1\.5 {
    padding-inline: calc(var(--spacing) * 1.5);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-5 {
    padding-inline: calc(var(--spacing) * 5);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .\!py-2 {
    padding-block: calc(var(--spacing) * 2) !important;
  }
  .\!py-3 {
    padding-block: calc(var(--spacing) * 3) !important;
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-5 {
    padding-block: calc(var(--spacing) * 5);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }
  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }
  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }
  .ps-1\.5 {
    padding-inline-start: calc(var(--spacing) * 1.5);
  }
  .ps-2 {
    padding-inline-start: calc(var(--spacing) * 2);
  }
  .ps-3 {
    padding-inline-start: calc(var(--spacing) * 3);
  }
  .ps-3\.5 {
    padding-inline-start: calc(var(--spacing) * 3.5);
  }
  .ps-4 {
    padding-inline-start: calc(var(--spacing) * 4);
  }
  .ps-9 {
    padding-inline-start: calc(var(--spacing) * 9);
  }
  .ps-10 {
    padding-inline-start: calc(var(--spacing) * 10);
  }
  .ps-\[38px\] {
    padding-inline-start: 38px;
  }
  .\!pe-6 {
    padding-inline-end: calc(var(--spacing) * 6) !important;
  }
  .pe-1 {
    padding-inline-end: calc(var(--spacing) * 1);
  }
  .pe-3 {
    padding-inline-end: calc(var(--spacing) * 3);
  }
  .pe-3\.5 {
    padding-inline-end: calc(var(--spacing) * 3.5);
  }
  .pe-4 {
    padding-inline-end: calc(var(--spacing) * 4);
  }
  .pe-10 {
    padding-inline-end: calc(var(--spacing) * 10);
  }
  .pe-14 {
    padding-inline-end: calc(var(--spacing) * 14);
  }
  .\!pt-2 {
    padding-top: calc(var(--spacing) * 2) !important;
  }
  .\!pt-4 {
    padding-top: calc(var(--spacing) * 4) !important;
  }
  .pt-0 {
    padding-top: calc(var(--spacing) * 0);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }
  .pt-2\.5 {
    padding-top: calc(var(--spacing) * 2.5);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-5 {
    padding-top: calc(var(--spacing) * 5);
  }
  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }
  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }
  .pt-20 {
    padding-top: calc(var(--spacing) * 20);
  }
  .pt-\[62px\] {
    padding-top: 62px;
  }
  .search-filtering-row {
    .datatable-wrapper .datatable-container thead tr& th {
      padding-top: 0;
    }
  }
  .apexcharts-legend-text {
    .apexcharts-canvas & {
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      padding-left: 1.25rem !important;
      color: var(--color-gray-500) !important;
    }
    :is([dir=rtl]) .apexcharts-canvas & {
      padding-right: 0.5rem !important;
    }
    .apexcharts-canvas &:not(.apexcharts-inactive-legend):hover {
      color: var(--color-gray-900) !important;
    }
    .dark .apexcharts-canvas & {
      color: var(--color-gray-400) !important;
    }
    .dark .apexcharts-canvas &:not(.apexcharts-inactive-legend):hover {
      color: white !important;
    }
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }
  .pr-5 {
    padding-right: calc(var(--spacing) * 5);
  }
  .\!pb-4 {
    padding-bottom: calc(var(--spacing) * 4) !important;
  }
  .pb-0 {
    padding-bottom: calc(var(--spacing) * 0);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-2\.5 {
    padding-bottom: calc(var(--spacing) * 2.5);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-5 {
    padding-bottom: calc(var(--spacing) * 5);
  }
  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-5 {
    padding-left: calc(var(--spacing) * 5);
  }
  .pl-9 {
    padding-left: calc(var(--spacing) * 9);
  }
  .pl-10 {
    padding-left: calc(var(--spacing) * 10);
  }
  .\!text-left {
    text-align: left !important;
  }
  .datatable-empty {
    .datatable-wrapper .datatable-table & {
      text-align: center;
    }
  }
  .text-center {
    text-align: center;
  }
  .text-end {
    text-align: end;
  }
  .text-left {
    text-align: left;
  }
  .text-start {
    text-align: start;
  }
  .align-middle {
    vertical-align: middle;
  }
  .font-sans {
    font-family: var(--font-sans);
  }
  .\!text-base {
    font-size: var(--text-base) !important;
    line-height: var(--tw-leading, var(--text-base--line-height)) !important;
  }
  .\!text-lg {
    font-size: var(--text-lg) !important;
    line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  }
  .\!text-sm {
    font-size: var(--text-sm) !important;
    line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  }
  .\!text-xs {
    font-size: var(--text-xs) !important;
    line-height: var(--tw-leading, var(--text-xs--line-height)) !important;
  }
  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }
  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }
  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }
  .text-5xl {
    font-size: var(--text-5xl);
    line-height: var(--tw-leading, var(--text-5xl--line-height));
  }
  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }
  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }
  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }
  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }
  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }
  .apexcharts-datalabels {
    .apexcharts-canvas & .apexcharts-text.apexcharts-pie-label {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-pie-label {
    .apexcharts-canvas .apexcharts-datalabels .apexcharts-text& {
      font-size: 0.75rem,[object Object] !important;
      font-weight: 600 !important;
      text-shadow: none !important;
      filter: none !important;
    }
  }
  .apexcharts-xaxistooltip-text {
    .apexcharts-canvas & {
      font-weight: 400 !important;
      font-size: 0.875rem !important;
    }
  }
  .apexcharts-tooltip-text-y-label {
    .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-500) !important;
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-400) !important;
    }
  }
  .apexcharts-tooltip-text-y-value {
    .apexcharts-canvas .apexcharts-tooltip & {
      color: var(--color-gray-900);
      font-size: 0.875rem !important;
    }
    .dark .apexcharts-canvas .apexcharts-tooltip & {
      color: white !important;
    }
  }
  .datatable-info {
    .datatable-wrapper .datatable-bottom & {
      color: var(--color-gray-500);
      font-size: 0.875rem;
    }
    .dark .datatable-wrapper .datatable-bottom & {
      color: var(--color-gray-400);
    }
  }
  .leading-1\.5 {
    --tw-leading: calc(var(--spacing) * 1.5);
    line-height: calc(var(--spacing) * 1.5);
  }
  .leading-6 {
    --tw-leading: calc(var(--spacing) * 6);
    line-height: calc(var(--spacing) * 6);
  }
  .leading-9 {
    --tw-leading: calc(var(--spacing) * 9);
    line-height: calc(var(--spacing) * 9);
  }
  .leading-none {
    --tw-leading: 1;
    line-height: 1;
  }
  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }
  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }
  .\!font-medium {
    --tw-font-weight: var(--font-weight-medium) !important;
    font-weight: var(--font-weight-medium) !important;
  }
  .\!font-normal {
    --tw-font-weight: var(--font-weight-normal) !important;
    font-weight: var(--font-weight-normal) !important;
  }
  .\!font-semibold {
    --tw-font-weight: var(--font-weight-semibold) !important;
    font-weight: var(--font-weight-semibold) !important;
  }
  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }
  .font-extrabold {
    --tw-font-weight: var(--font-weight-extrabold);
    font-weight: var(--font-weight-extrabold);
  }
  .font-light {
    --tw-font-weight: var(--font-weight-light);
    font-weight: var(--font-weight-light);
  }
  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }
  .font-normal {
    --tw-font-weight: var(--font-weight-normal);
    font-weight: var(--font-weight-normal);
  }
  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }
  .tracking-tight {
    --tw-tracking: var(--tracking-tight);
    letter-spacing: var(--tracking-tight);
  }
  .text-nowrap {
    text-wrap: nowrap;
  }
  .break-all {
    word-break: break-all;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre {
    white-space: pre;
  }
  .datatable-sorter {
    .datatable-wrapper .datatable-table thead th & {
      text-transform: uppercase;
    }
    .datatable-wrapper .datatable-table thead th &:hover {
      color: var(--color-gray-900);
    }
    .datatable-wrapper .datatable-table thead th.datatable-ascending & {
      color: var(--color-gray-900);
    }
    .datatable-wrapper .datatable-table thead th.datatable-descending & {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th &:hover {
      color: white;
    }
    .dark .datatable-wrapper .datatable-table thead th.datatable-ascending & {
      color: white;
    }
    .dark .datatable-wrapper .datatable-table thead th.datatable-descending & {
      color: white;
    }
  }
  .datatable-ascending {
    .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: white;
    }
  }
  .datatable-descending {
    .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: var(--color-gray-900);
    }
    .dark .datatable-wrapper .datatable-table thead th& .datatable-sorter {
      color: white;
    }
  }
  .\!text-gray-500 {
    color: var(--color-gray-500) !important;
  }
  .\!text-gray-900 {
    color: var(--color-gray-900) !important;
  }
  .\!text-green-700 {
    color: var(--color-green-700) !important;
  }
  .\!text-indigo-700 {
    color: var(--color-indigo-700) !important;
  }
  .\!text-orange-700 {
    color: var(--color-orange-700) !important;
  }
  .\!text-pink-700 {
    color: var(--color-pink-700) !important;
  }
  .\!text-primary-700 {
    color: var(--color-primary-700) !important;
  }
  .\!text-purple-700 {
    color: var(--color-purple-700) !important;
  }
  .\!text-red-700 {
    color: var(--color-red-700) !important;
  }
  .\!text-teal-700 {
    color: var(--color-teal-700) !important;
  }
  .\!text-white {
    color: var(--color-white) !important;
  }
  .\!text-yellow-700 {
    color: var(--color-yellow-700) !important;
  }
  .text-\[\#1434CB\] {
    color: #1434CB;
  }
  .text-blue-600 {
    color: var(--color-blue-600);
  }
  .text-gray-300 {
    color: var(--color-gray-300);
  }
  .text-gray-400 {
    color: var(--color-gray-400);
  }
  .text-gray-500 {
    color: var(--color-gray-500);
  }
  .text-gray-600 {
    color: var(--color-gray-600);
  }
  .text-gray-700 {
    color: var(--color-gray-700);
  }
  .text-gray-800 {
    color: var(--color-gray-800);
  }
  .text-gray-900 {
    color: var(--color-gray-900);
  }
  .text-green-500 {
    color: var(--color-green-500);
  }
  .text-green-600 {
    color: var(--color-green-600);
  }
  .text-green-700 {
    color: var(--color-green-700);
  }
  .text-green-800 {
    color: var(--color-green-800);
  }
  .text-indigo-700 {
    color: var(--color-indigo-700);
  }
  .text-indigo-800 {
    color: var(--color-indigo-800);
  }
  .text-orange-400 {
    color: var(--color-orange-400);
  }
  .text-orange-600 {
    color: var(--color-orange-600);
  }
  .text-orange-700 {
    color: var(--color-orange-700);
  }
  .text-pink-700 {
    color: var(--color-pink-700);
  }
  .text-primary-100 {
    color: var(--color-primary-100);
  }
  .text-primary-500 {
    color: var(--color-primary-500);
  }
  .text-primary-600 {
    color: var(--color-primary-600);
  }
  .text-primary-700 {
    color: var(--color-primary-700);
  }
  .text-primary-800 {
    color: var(--color-primary-800);
  }
  .text-purple-600 {
    color: var(--color-purple-600);
  }
  .text-purple-700 {
    color: var(--color-purple-700);
  }
  .text-purple-800 {
    color: var(--color-purple-800);
  }
  .text-red-500 {
    color: var(--color-red-500);
  }
  .text-red-600 {
    color: var(--color-red-600);
  }
  .text-red-700 {
    color: var(--color-red-700);
  }
  .text-red-800 {
    color: var(--color-red-800);
  }
  .text-teal-600 {
    color: var(--color-teal-600);
  }
  .text-teal-700 {
    color: var(--color-teal-700);
  }
  .text-white {
    color: var(--color-white);
  }
  .text-yellow-300 {
    color: var(--color-yellow-300);
  }
  .text-yellow-400 {
    color: var(--color-yellow-400);
  }
  .text-yellow-500 {
    color: var(--color-yellow-500);
  }
  .text-yellow-700 {
    color: var(--color-yellow-700);
  }
  .text-yellow-800 {
    color: var(--color-yellow-800);
  }
  .lowercase {
    text-transform: lowercase;
  }
  .uppercase {
    text-transform: uppercase;
  }
  .italic {
    font-style: italic;
  }
  .line-through {
    text-decoration-line: line-through;
  }
  .underline {
    text-decoration-line: underline;
  }
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  .\!opacity-100 {
    opacity: 100% !important;
  }
  .opacity-0 {
    opacity: 0%;
  }
  .opacity-100 {
    opacity: 100%;
  }
  .bg-blend-multiply {
    background-blend-mode: multiply;
  }
  .\!shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1)) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  .\!shadow-none {
    --tw-shadow: 0 0 #0000 !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-none {
    --tw-shadow: 0 0 #0000;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-xs {
    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-2 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-8 {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .ring-transparent {
    --tw-ring-color: transparent;
  }
  .ring-white {
    --tw-ring-color: var(--color-white);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .invert {
    --tw-invert: invert(100%);
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .filter {
    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
  }
  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-opacity {
    transition-property: opacity;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }
  .duration-75 {
    --tw-duration: 75ms;
    transition-duration: 75ms;
  }
  .duration-300 {
    --tw-duration: 300ms;
    transition-duration: 300ms;
  }
  .duration-700 {
    --tw-duration: 700ms;
    transition-duration: 700ms;
  }
  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }
  .format-blue {
    --tw-format-links: oklch(54.6% 0.245 262.881);
    --tw-format-invert-links: oklch(62.3% 0.214 259.815);
  }
  .group-hover\:bg-gray-50 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .group-hover\:bg-primary-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-primary-200);
      }
    }
  }
  .group-hover\:bg-purple-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-purple-200);
      }
    }
  }
  .group-hover\:bg-teal-200 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: var(--color-teal-200);
      }
    }
  }
  .group-hover\:bg-white\/50 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #ffffff 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
        }
      }
    }
  }
  .group-hover\:text-gray-500 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-500);
      }
    }
  }
  .group-hover\:text-gray-900 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .group-hover\:text-primary-700 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        color: var(--color-primary-700);
      }
    }
  }
  .group-hover\:underline {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .group-hover\:opacity-100 {
    &:is(:where(.group):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-hover\/image\:opacity-100 {
    &:is(:where(.group\/image):hover *) {
      @media (hover: hover) {
        opacity: 100%;
      }
    }
  }
  .group-focus\:ring-4 {
    &:is(:where(.group):focus *) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .group-focus\:ring-white {
    &:is(:where(.group):focus *) {
      --tw-ring-color: var(--color-white);
    }
  }
  .group-focus\:outline-none {
    &:is(:where(.group):focus *) {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .peer-checked\:border-primary-700 {
    &:is(:where(.peer):checked ~ *) {
      border-color: var(--color-primary-700);
    }
  }
  .peer-checked\:bg-primary-600 {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-primary-600);
    }
  }
  .peer-checked\:bg-primary-700 {
    &:is(:where(.peer):checked ~ *) {
      background-color: var(--color-primary-700);
    }
  }
  .peer-checked\:text-white {
    &:is(:where(.peer):checked ~ *) {
      color: var(--color-white);
    }
  }
  .peer-placeholder-shown\:translate-y-0 {
    &:is(:where(.peer):placeholder-shown ~ *) {
      --tw-translate-y: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .peer-placeholder-shown\:scale-100 {
    &:is(:where(.peer):placeholder-shown ~ *) {
      --tw-scale-x: 100%;
      --tw-scale-y: 100%;
      --tw-scale-z: 100%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .peer-focus\:start-0 {
    &:is(:where(.peer):focus ~ *) {
      inset-inline-start: calc(var(--spacing) * 0);
    }
  }
  .peer-focus\:-translate-y-6 {
    &:is(:where(.peer):focus ~ *) {
      --tw-translate-y: calc(var(--spacing) * -6);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .peer-focus\:scale-75 {
    &:is(:where(.peer):focus ~ *) {
      --tw-scale-x: 75%;
      --tw-scale-y: 75%;
      --tw-scale-z: 75%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }
  .peer-focus\:text-primary-600 {
    &:is(:where(.peer):focus ~ *) {
      color: var(--color-primary-600);
    }
  }
  .peer-focus\:ring-4 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .peer-focus\:ring-primary-300 {
    &:is(:where(.peer):focus ~ *) {
      --tw-ring-color: var(--color-primary-300);
    }
  }
  .peer-focus\:outline-none {
    &:is(:where(.peer):focus ~ *) {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .placeholder\:text-gray-500 {
    &::placeholder {
      color: var(--color-gray-500);
    }
  }
  .placeholder\:text-gray-900 {
    &::placeholder {
      color: var(--color-gray-900);
    }
  }
  .after\:absolute {
    &::after {
      content: var(--tw-content);
      position: absolute;
    }
  }
  .after\:start-\[2px\] {
    &::after {
      content: var(--tw-content);
      inset-inline-start: 2px;
    }
  }
  .after\:top-0\.5 {
    &::after {
      content: var(--tw-content);
      top: calc(var(--spacing) * 0.5);
    }
  }
  .after\:top-\[2px\] {
    &::after {
      content: var(--tw-content);
      top: 2px;
    }
  }
  .after\:left-\[2px\] {
    &::after {
      content: var(--tw-content);
      left: 2px;
    }
  }
  .after\:h-4 {
    &::after {
      content: var(--tw-content);
      height: calc(var(--spacing) * 4);
    }
  }
  .after\:h-5 {
    &::after {
      content: var(--tw-content);
      height: calc(var(--spacing) * 5);
    }
  }
  .after\:w-4 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 4);
    }
  }
  .after\:w-5 {
    &::after {
      content: var(--tw-content);
      width: calc(var(--spacing) * 5);
    }
  }
  .after\:rounded-full {
    &::after {
      content: var(--tw-content);
      border-radius: calc(infinity * 1px);
    }
  }
  .after\:border {
    &::after {
      content: var(--tw-content);
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .after\:border-gray-300 {
    &::after {
      content: var(--tw-content);
      border-color: var(--color-gray-300);
    }
  }
  .after\:bg-white {
    &::after {
      content: var(--tw-content);
      background-color: var(--color-white);
    }
  }
  .after\:transition-all {
    &::after {
      content: var(--tw-content);
      transition-property: all;
      transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
      transition-duration: var(--tw-duration, var(--default-transition-duration));
    }
  }
  .after\:content-\[\'\'\] {
    &::after {
      content: var(--tw-content);
      --tw-content: '';
      content: var(--tw-content);
    }
  }
  .peer-checked\:after\:translate-x-full {
    &:is(:where(.peer):checked ~ *) {
      &::after {
        content: var(--tw-content);
        --tw-translate-x: 100%;
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .peer-checked\:after\:border-white {
    &:is(:where(.peer):checked ~ *) {
      &::after {
        content: var(--tw-content);
        border-color: var(--color-white);
      }
    }
  }
  .hover\:cursor-pointer {
    &:hover {
      @media (hover: hover) {
        cursor: pointer;
      }
    }
  }
  .hover\:border-b-2 {
    &:hover {
      @media (hover: hover) {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 2px;
      }
    }
  }
  .hover\:border-gray-300 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-gray-300);
      }
    }
  }
  .hover\:border-primary-600 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary-600);
      }
    }
  }
  .hover\:border-primary-700 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary-700);
      }
    }
  }
  .hover\:border-primary-800 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-primary-800);
      }
    }
  }
  .hover\:border-red-800 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-red-800);
      }
    }
  }
  .hover\:\!bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50) !important;
      }
    }
  }
  .hover\:\!bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100) !important;
      }
    }
  }
  .hover\:\!bg-green-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-100) !important;
      }
    }
  }
  .hover\:\!bg-indigo-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-100) !important;
      }
    }
  }
  .hover\:\!bg-orange-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-100) !important;
      }
    }
  }
  .hover\:\!bg-pink-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-pink-100) !important;
      }
    }
  }
  .hover\:\!bg-primary-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-100) !important;
      }
    }
  }
  .hover\:\!bg-primary-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-800) !important;
      }
    }
  }
  .hover\:\!bg-purple-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-100) !important;
      }
    }
  }
  .hover\:\!bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100) !important;
      }
    }
  }
  .hover\:\!bg-teal-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-100) !important;
      }
    }
  }
  .hover\:\!bg-yellow-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-100) !important;
      }
    }
  }
  .hover\:bg-\[\#4285F4\]\/90 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in oklab, #4285F4 90%, transparent);
      }
    }
  }
  .hover\:bg-blue-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-blue-800);
      }
    }
  }
  .hover\:bg-gray-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-50);
      }
    }
  }
  .hover\:bg-gray-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-100);
      }
    }
  }
  .hover\:bg-gray-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-200);
      }
    }
  }
  .hover\:bg-gray-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-500);
      }
    }
  }
  .hover\:bg-gray-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600);
      }
    }
  }
  .hover\:bg-gray-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700);
      }
    }
  }
  .hover\:bg-gray-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-800);
      }
    }
  }
  .hover\:bg-green-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-100);
      }
    }
  }
  .hover\:bg-green-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-600);
      }
    }
  }
  .hover\:bg-green-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-700);
      }
    }
  }
  .hover\:bg-indigo-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-100);
      }
    }
  }
  .hover\:bg-indigo-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-500);
      }
    }
  }
  .hover\:bg-orange-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-100);
      }
    }
  }
  .hover\:bg-orange-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-700);
      }
    }
  }
  .hover\:bg-pink-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-pink-100);
      }
    }
  }
  .hover\:bg-pink-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-pink-500);
      }
    }
  }
  .hover\:bg-primary-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-50);
      }
    }
  }
  .hover\:bg-primary-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-100);
      }
    }
  }
  .hover\:bg-primary-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-500);
      }
    }
  }
  .hover\:bg-primary-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-700);
      }
    }
  }
  .hover\:bg-primary-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-800);
      }
    }
  }
  .hover\:bg-purple-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-100);
      }
    }
  }
  .hover\:bg-purple-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-500);
      }
    }
  }
  .hover\:bg-red-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-50);
      }
    }
  }
  .hover\:bg-red-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-100);
      }
    }
  }
  .hover\:bg-red-600 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-600);
      }
    }
  }
  .hover\:bg-red-700 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-700);
      }
    }
  }
  .hover\:bg-red-800 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-800);
      }
    }
  }
  .hover\:bg-teal-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-100);
      }
    }
  }
  .hover\:bg-teal-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-500);
      }
    }
  }
  .hover\:bg-white {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-white);
      }
    }
  }
  .hover\:bg-white\/50 {
    &:hover {
      @media (hover: hover) {
        background-color: color-mix(in srgb, #ffffff 50%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          background-color: color-mix(in oklab, var(--color-white) 50%, transparent);
        }
      }
    }
  }
  .hover\:bg-yellow-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-100);
      }
    }
  }
  .hover\:\!text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900) !important;
      }
    }
  }
  .hover\:\!text-primary-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary-700) !important;
      }
    }
  }
  .hover\:text-blue-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-blue-600);
      }
    }
  }
  .hover\:text-gray-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-600);
      }
    }
  }
  .hover\:text-gray-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-700);
      }
    }
  }
  .hover\:text-gray-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-800);
      }
    }
  }
  .hover\:text-gray-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-gray-900);
      }
    }
  }
  .hover\:text-primary-600 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary-600);
      }
    }
  }
  .hover\:text-primary-700 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary-700);
      }
    }
  }
  .hover\:text-primary-800 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary-800);
      }
    }
  }
  .hover\:text-primary-900 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-primary-900);
      }
    }
  }
  .hover\:text-red-500 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-red-500);
      }
    }
  }
  .hover\:text-white {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .hover\:text-yellow-400 {
    &:hover {
      @media (hover: hover) {
        color: var(--color-yellow-400);
      }
    }
  }
  .hover\:no-underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: none;
      }
    }
  }
  .hover\:underline {
    &:hover {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .focus\:\!z-10 {
    &:focus {
      z-index: 10 !important;
    }
  }
  .focus\:z-10 {
    &:focus {
      z-index: 10;
    }
  }
  .focus\:border-gray-200 {
    &:focus {
      border-color: var(--color-gray-200);
    }
  }
  .focus\:border-primary-500 {
    &:focus {
      border-color: var(--color-primary-500);
    }
  }
  .focus\:border-primary-600 {
    &:focus {
      border-color: var(--color-primary-600);
    }
  }
  .focus\:\!bg-gray-100 {
    &:focus {
      background-color: var(--color-gray-100) !important;
    }
  }
  .focus\:bg-gray-100 {
    &:focus {
      background-color: var(--color-gray-100);
    }
  }
  .focus\:text-primary-700 {
    &:focus {
      color: var(--color-primary-700);
    }
  }
  .focus\:\!ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
    }
  }
  .focus\:\!ring-4 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
    }
  }
  .focus\:ring-0 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-1 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-2 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-3 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:ring-4 {
    &:focus {
      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .focus\:\!ring-gray-100 {
    &:focus {
      --tw-ring-color: var(--color-gray-100) !important;
    }
  }
  .focus\:\!ring-primary-300 {
    &:focus {
      --tw-ring-color: var(--color-primary-300) !important;
    }
  }
  .focus\:ring-\[\#4285F4\]\/50 {
    &:focus {
      --tw-ring-color: color-mix(in oklab, #4285F4 50%, transparent);
    }
  }
  .focus\:ring-blue-300 {
    &:focus {
      --tw-ring-color: var(--color-blue-300);
    }
  }
  .focus\:ring-gray-50 {
    &:focus {
      --tw-ring-color: var(--color-gray-50);
    }
  }
  .focus\:ring-gray-100 {
    &:focus {
      --tw-ring-color: var(--color-gray-100);
    }
  }
  .focus\:ring-gray-200 {
    &:focus {
      --tw-ring-color: var(--color-gray-200);
    }
  }
  .focus\:ring-gray-300 {
    &:focus {
      --tw-ring-color: var(--color-gray-300);
    }
  }
  .focus\:ring-green-100 {
    &:focus {
      --tw-ring-color: var(--color-green-100);
    }
  }
  .focus\:ring-primary-100 {
    &:focus {
      --tw-ring-color: var(--color-primary-100);
    }
  }
  .focus\:ring-primary-200 {
    &:focus {
      --tw-ring-color: var(--color-primary-200);
    }
  }
  .focus\:ring-primary-300 {
    &:focus {
      --tw-ring-color: var(--color-primary-300);
    }
  }
  .focus\:ring-primary-500 {
    &:focus {
      --tw-ring-color: var(--color-primary-500);
    }
  }
  .focus\:ring-primary-600 {
    &:focus {
      --tw-ring-color: var(--color-primary-600);
    }
  }
  .focus\:ring-primary-700 {
    &:focus {
      --tw-ring-color: var(--color-primary-700);
    }
  }
  .focus\:ring-red-200 {
    &:focus {
      --tw-ring-color: var(--color-red-200);
    }
  }
  .focus\:ring-red-300 {
    &:focus {
      --tw-ring-color: var(--color-red-300);
    }
  }
  .focus\:\!outline-none {
    &:focus {
      --tw-outline-style: none !important;
      outline-style: none !important;
    }
  }
  .focus\:outline-none {
    &:focus {
      --tw-outline-style: none;
      outline-style: none;
    }
  }
  .active\:\!bg-primary-800 {
    &:active {
      background-color: var(--color-primary-800) !important;
    }
  }
  .active\:bg-primary-800 {
    &:active {
      background-color: var(--color-primary-800);
    }
  }
  .data-\[color-selected\=true\]\:ring-green-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-green-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-indigo-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-indigo-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-orange-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-orange-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-pink-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-pink-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-primary-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-primary-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-purple-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-purple-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-red-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-red-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-teal-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-teal-300);
    }
  }
  .data-\[color-selected\=true\]\:ring-yellow-300 {
    &[data-color-selected="true"] {
      --tw-ring-color: var(--color-yellow-300);
    }
  }
  .min-\[1928px\]\:flex {
    @media (width >= 1928px) {
      display: flex;
    }
  }
  .sm\:relative {
    @media (width >= 40rem) {
      position: relative;
    }
  }
  .sm\:end-0 {
    @media (width >= 40rem) {
      inset-inline-end: calc(var(--spacing) * 0);
    }
  }
  .sm\:top-0 {
    @media (width >= 40rem) {
      top: calc(var(--spacing) * 0);
    }
  }
  .sm\:col-span-1 {
    @media (width >= 40rem) {
      grid-column: span 1 / span 1;
    }
  }
  .sm\:col-span-2 {
    @media (width >= 40rem) {
      grid-column: span 2 / span 2;
    }
  }
  .sm\:col-span-5 {
    @media (width >= 40rem) {
      grid-column: span 5 / span 5;
    }
  }
  .sm\:col-span-7 {
    @media (width >= 40rem) {
      grid-column: span 7 / span 7;
    }
  }
  .sm\:mx-0 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .sm\:mx-4 {
    @media (width >= 40rem) {
      margin-inline: calc(var(--spacing) * 4);
    }
  }
  .sm\:mx-auto {
    @media (width >= 40rem) {
      margin-inline: auto;
    }
  }
  .sm\:my-6 {
    @media (width >= 40rem) {
      margin-block: calc(var(--spacing) * 6);
    }
  }
  .sm\:-ms-1 {
    @media (width >= 40rem) {
      margin-inline-start: calc(var(--spacing) * -1);
    }
  }
  .sm\:ms-0 {
    @media (width >= 40rem) {
      margin-inline-start: calc(var(--spacing) * 0);
    }
  }
  .sm\:ms-4 {
    @media (width >= 40rem) {
      margin-inline-start: calc(var(--spacing) * 4);
    }
  }
  .sm\:ms-7 {
    @media (width >= 40rem) {
      margin-inline-start: calc(var(--spacing) * 7);
    }
  }
  .sm\:ms-auto {
    @media (width >= 40rem) {
      margin-inline-start: auto;
    }
  }
  .sm\:me-0 {
    @media (width >= 40rem) {
      margin-inline-end: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-0 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:mt-5 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 5);
    }
  }
  .sm\:mt-6 {
    @media (width >= 40rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .sm\:mr-2 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 2);
    }
  }
  .sm\:mr-4 {
    @media (width >= 40rem) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .sm\:mb-0 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:mb-1\.5 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 1.5);
    }
  }
  .sm\:mb-3 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 3);
    }
  }
  .sm\:mb-4 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .sm\:mb-5 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }
  .sm\:mb-6 {
    @media (width >= 40rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .sm\:\!ml-4 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 4) !important;
    }
  }
  .sm\:ml-4 {
    @media (width >= 40rem) {
      margin-left: calc(var(--spacing) * 4);
    }
  }
  .sm\:ml-auto {
    @media (width >= 40rem) {
      margin-left: auto;
    }
  }
  .sm\:block {
    @media (width >= 40rem) {
      display: block;
    }
  }
  .sm\:flex {
    @media (width >= 40rem) {
      display: flex;
    }
  }
  .sm\:grid {
    @media (width >= 40rem) {
      display: grid;
    }
  }
  .sm\:hidden {
    @media (width >= 40rem) {
      display: none;
    }
  }
  .sm\:inline {
    @media (width >= 40rem) {
      display: inline;
    }
  }
  .sm\:inline-flex {
    @media (width >= 40rem) {
      display: inline-flex;
    }
  }
  .sm\:h-3\.5 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 3.5);
    }
  }
  .sm\:h-8 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 8);
    }
  }
  .sm\:h-16 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 16);
    }
  }
  .sm\:h-24 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 24);
    }
  }
  .sm\:h-36 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 36);
    }
  }
  .sm\:h-40 {
    @media (width >= 40rem) {
      height: calc(var(--spacing) * 40);
    }
  }
  .sm\:h-\[calc\(100vh-15\.4rem\)\] {
    @media (width >= 40rem) {
      height: calc(100vh - 15.4rem);
    }
  }
  .sm\:\!w-auto {
    @media (width >= 40rem) {
      width: auto !important;
    }
  }
  .sm\:w-1\/2 {
    @media (width >= 40rem) {
      width: calc(1/2 * 100%);
    }
  }
  .sm\:w-3\.5 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 3.5);
    }
  }
  .sm\:w-8 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 8);
    }
  }
  .sm\:w-16 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 16);
    }
  }
  .sm\:w-24 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 24);
    }
  }
  .sm\:w-36 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 36);
    }
  }
  .sm\:w-40 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 40);
    }
  }
  .sm\:w-96 {
    @media (width >= 40rem) {
      width: calc(var(--spacing) * 96);
    }
  }
  .sm\:w-auto {
    @media (width >= 40rem) {
      width: auto;
    }
  }
  .sm\:w-full {
    @media (width >= 40rem) {
      width: 100%;
    }
  }
  .sm\:max-w-60 {
    @media (width >= 40rem) {
      max-width: calc(var(--spacing) * 60);
    }
  }
  .sm\:max-w-xl {
    @media (width >= 40rem) {
      max-width: var(--container-xl);
    }
  }
  .sm\:min-w-64 {
    @media (width >= 40rem) {
      min-width: calc(var(--spacing) * 64);
    }
  }
  .sm\:min-w-72 {
    @media (width >= 40rem) {
      min-width: calc(var(--spacing) * 72);
    }
  }
  .sm\:flex-none {
    @media (width >= 40rem) {
      flex: none;
    }
  }
  .sm\:grid-cols-2 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-3 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .sm\:grid-cols-4 {
    @media (width >= 40rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .sm\:\!flex-row {
    @media (width >= 40rem) {
      flex-direction: row !important;
    }
  }
  .sm\:flex-row {
    @media (width >= 40rem) {
      flex-direction: row;
    }
  }
  .sm\:items-center {
    @media (width >= 40rem) {
      align-items: center;
    }
  }
  .sm\:justify-between {
    @media (width >= 40rem) {
      justify-content: space-between;
    }
  }
  .sm\:justify-end {
    @media (width >= 40rem) {
      justify-content: flex-end;
    }
  }
  .sm\:justify-start {
    @media (width >= 40rem) {
      justify-content: flex-start;
    }
  }
  .sm\:gap-0 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 0);
    }
  }
  .sm\:gap-6 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .sm\:gap-8 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .sm\:gap-24 {
    @media (width >= 40rem) {
      gap: calc(var(--spacing) * 24);
    }
  }
  .sm\:space-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-y-6 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-y-8 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .sm\:space-x-3 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-4 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-5 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 5) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-8 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:space-x-24 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 24) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 24) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .sm\:divide-x {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-x-reverse: 0;
        border-inline-style: var(--tw-border-style);
        border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
        border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
      }
    }
  }
  .sm\:divide-y-0 {
    @media (width >= 40rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-y-reverse: 0;
        border-bottom-style: var(--tw-border-style);
        border-top-style: var(--tw-border-style);
        border-top-width: calc(0px * var(--tw-divide-y-reverse));
        border-bottom-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
      }
    }
  }
  .sm\:rounded-lg {
    @media (width >= 40rem) {
      border-radius: var(--radius-lg);
    }
  }
  .sm\:border-0 {
    @media (width >= 40rem) {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .sm\:border-s {
    @media (width >= 40rem) {
      border-inline-start-style: var(--tw-border-style);
      border-inline-start-width: 1px;
    }
  }
  .sm\:border-e {
    @media (width >= 40rem) {
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 1px;
    }
  }
  .sm\:border-b-0 {
    @media (width >= 40rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .sm\:border-gray-200 {
    @media (width >= 40rem) {
      border-color: var(--color-gray-200);
    }
  }
  .sm\:border-transparent {
    @media (width >= 40rem) {
      border-color: transparent;
    }
  }
  .sm\:p-5 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .sm\:p-6 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .sm\:p-8 {
    @media (width >= 40rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .sm\:px-2 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 2);
    }
  }
  .sm\:px-5 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .sm\:px-6 {
    @media (width >= 40rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .sm\:py-3\.5 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 3.5);
    }
  }
  .sm\:py-4 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 4);
    }
  }
  .sm\:py-5 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 5);
    }
  }
  .sm\:py-6 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 6);
    }
  }
  .sm\:py-16 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .sm\:py-28 {
    @media (width >= 40rem) {
      padding-block: calc(var(--spacing) * 28);
    }
  }
  .sm\:ps-2 {
    @media (width >= 40rem) {
      padding-inline-start: calc(var(--spacing) * 2);
    }
  }
  .sm\:ps-4 {
    @media (width >= 40rem) {
      padding-inline-start: calc(var(--spacing) * 4);
    }
  }
  .sm\:ps-5 {
    @media (width >= 40rem) {
      padding-inline-start: calc(var(--spacing) * 5);
    }
  }
  .sm\:ps-6 {
    @media (width >= 40rem) {
      padding-inline-start: calc(var(--spacing) * 6);
    }
  }
  .sm\:pe-3 {
    @media (width >= 40rem) {
      padding-inline-end: calc(var(--spacing) * 3);
    }
  }
  .sm\:pe-4 {
    @media (width >= 40rem) {
      padding-inline-end: calc(var(--spacing) * 4);
    }
  }
  .sm\:pe-6 {
    @media (width >= 40rem) {
      padding-inline-end: calc(var(--spacing) * 6);
    }
  }
  .sm\:pt-0 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .sm\:pt-4 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 4);
    }
  }
  .sm\:pt-5 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 5);
    }
  }
  .sm\:pt-6 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 6);
    }
  }
  .sm\:pt-16 {
    @media (width >= 40rem) {
      padding-top: calc(var(--spacing) * 16);
    }
  }
  .sm\:pr-2 {
    @media (width >= 40rem) {
      padding-right: calc(var(--spacing) * 2);
    }
  }
  .sm\:pb-0 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .sm\:pb-2\.5 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 2.5);
    }
  }
  .sm\:pb-5 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 5);
    }
  }
  .sm\:pb-6 {
    @media (width >= 40rem) {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }
  .sm\:pl-2 {
    @media (width >= 40rem) {
      padding-left: calc(var(--spacing) * 2);
    }
  }
  .sm\:text-center {
    @media (width >= 40rem) {
      text-align: center;
    }
  }
  .sm\:text-2xl {
    @media (width >= 40rem) {
      font-size: var(--text-2xl);
      line-height: var(--tw-leading, var(--text-2xl--line-height));
    }
  }
  .sm\:text-3xl {
    @media (width >= 40rem) {
      font-size: var(--text-3xl);
      line-height: var(--tw-leading, var(--text-3xl--line-height));
    }
  }
  .sm\:text-4xl {
    @media (width >= 40rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .sm\:text-base {
    @media (width >= 40rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .sm\:text-lg {
    @media (width >= 40rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .sm\:text-sm {
    @media (width >= 40rem) {
      font-size: var(--text-sm);
      line-height: var(--tw-leading, var(--text-sm--line-height));
    }
  }
  .sm\:text-xl {
    @media (width >= 40rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .sm\:leading-none {
    @media (width >= 40rem) {
      --tw-leading: 1;
      line-height: 1;
    }
  }
  .md\:sticky {
    @media (width >= 48rem) {
      position: sticky;
    }
  }
  .md\:inset-0 {
    @media (width >= 48rem) {
      inset: calc(var(--spacing) * 0);
    }
  }
  .md\:col-span-1 {
    @media (width >= 48rem) {
      grid-column: span 1 / span 1;
    }
  }
  .md\:col-span-6 {
    @media (width >= 48rem) {
      grid-column: span 6 / span 6;
    }
  }
  .md\:col-span-9 {
    @media (width >= 48rem) {
      grid-column: span 9 / span 9;
    }
  }
  .md\:my-2 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 2);
    }
  }
  .md\:my-5 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 5);
    }
  }
  .md\:my-6 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 6);
    }
  }
  .md\:my-8 {
    @media (width >= 48rem) {
      margin-block: calc(var(--spacing) * 8);
    }
  }
  .md\:ms-2 {
    @media (width >= 48rem) {
      margin-inline-start: calc(var(--spacing) * 2);
    }
  }
  .md\:ms-auto {
    @media (width >= 48rem) {
      margin-inline-start: auto;
    }
  }
  .md\:me-0 {
    @media (width >= 48rem) {
      margin-inline-end: calc(var(--spacing) * 0);
    }
  }
  .md\:mt-0 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .md\:mt-5 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 5);
    }
  }
  .md\:mt-6 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .md\:mt-8 {
    @media (width >= 48rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .md\:mr-4 {
    @media (width >= 48rem) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .md\:mb-0 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:mb-4 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .md\:mb-5 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 5);
    }
  }
  .md\:mb-6 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:mb-8 {
    @media (width >= 48rem) {
      margin-bottom: calc(var(--spacing) * 8);
    }
  }
  .md\:ml-2 {
    @media (width >= 48rem) {
      margin-left: calc(var(--spacing) * 2);
    }
  }
  .md\:block {
    @media (width >= 48rem) {
      display: block;
    }
  }
  .md\:flex {
    @media (width >= 48rem) {
      display: flex;
    }
  }
  .md\:grid {
    @media (width >= 48rem) {
      display: grid;
    }
  }
  .md\:hidden {
    @media (width >= 48rem) {
      display: none;
    }
  }
  .md\:inline-flex {
    @media (width >= 48rem) {
      display: inline-flex;
    }
  }
  .md\:h-\[40px\] {
    @media (width >= 48rem) {
      height: 40px;
    }
  }
  .md\:h-full {
    @media (width >= 48rem) {
      height: 100%;
    }
  }
  .md\:w-1\/2 {
    @media (width >= 48rem) {
      width: calc(1/2 * 100%);
    }
  }
  .md\:w-64 {
    @media (width >= 48rem) {
      width: calc(var(--spacing) * 64);
    }
  }
  .md\:w-\[185px\] {
    @media (width >= 48rem) {
      width: 185px;
    }
  }
  .md\:w-auto {
    @media (width >= 48rem) {
      width: auto;
    }
  }
  .md\:max-w-lg {
    @media (width >= 48rem) {
      max-width: var(--container-lg);
    }
  }
  .md\:max-w-md {
    @media (width >= 48rem) {
      max-width: var(--container-md);
    }
  }
  .md\:max-w-sm {
    @media (width >= 48rem) {
      max-width: var(--container-sm);
    }
  }
  .md\:grid-cols-2 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-4 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-12 {
    @media (width >= 48rem) {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }
  .md\:\!flex-row {
    @media (width >= 48rem) {
      flex-direction: row !important;
    }
  }
  .md\:flex-row {
    @media (width >= 48rem) {
      flex-direction: row;
    }
  }
  .md\:items-center {
    @media (width >= 48rem) {
      align-items: center;
    }
  }
  .md\:items-start {
    @media (width >= 48rem) {
      align-items: flex-start;
    }
  }
  .md\:justify-between {
    @media (width >= 48rem) {
      justify-content: space-between;
    }
  }
  .md\:justify-end {
    @media (width >= 48rem) {
      justify-content: flex-end;
    }
  }
  .md\:justify-start {
    @media (width >= 48rem) {
      justify-content: flex-start;
    }
  }
  .md\:gap-6 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 6);
    }
  }
  .md\:gap-24 {
    @media (width >= 48rem) {
      gap: calc(var(--spacing) * 24);
    }
  }
  .md\:space-y-0 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:space-y-6 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .md\:gap-x-8 {
    @media (width >= 48rem) {
      column-gap: calc(var(--spacing) * 8);
    }
  }
  .md\:space-x-2 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:space-x-3 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:space-x-4 {
    @media (width >= 48rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .md\:rounded-l-lg {
    @media (width >= 48rem) {
      border-top-left-radius: var(--radius-lg);
      border-bottom-left-radius: var(--radius-lg);
    }
  }
  .md\:rounded-r-lg {
    @media (width >= 48rem) {
      border-top-right-radius: var(--radius-lg);
      border-bottom-right-radius: var(--radius-lg);
    }
  }
  .md\:rounded-tr-none {
    @media (width >= 48rem) {
      border-top-right-radius: 0;
    }
  }
  .md\:rounded-bl-none {
    @media (width >= 48rem) {
      border-bottom-left-radius: 0;
    }
  }
  .md\:border-x-0 {
    @media (width >= 48rem) {
      border-inline-style: var(--tw-border-style);
      border-inline-width: 0px;
    }
  }
  .md\:border-t {
    @media (width >= 48rem) {
      border-top-style: var(--tw-border-style);
      border-top-width: 1px;
    }
  }
  .md\:border-b {
    @media (width >= 48rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .md\:border-l {
    @media (width >= 48rem) {
      border-left-style: var(--tw-border-style);
      border-left-width: 1px;
    }
  }
  .md\:p-5 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 5);
    }
  }
  .md\:p-6 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .md\:p-8 {
    @media (width >= 48rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .md\:px-5 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 5);
    }
  }
  .md\:px-6 {
    @media (width >= 48rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .md\:py-0 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .md\:py-6 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 6);
    }
  }
  .md\:py-12 {
    @media (width >= 48rem) {
      padding-block: calc(var(--spacing) * 12);
    }
  }
  .md\:pe-0 {
    @media (width >= 48rem) {
      padding-inline-end: calc(var(--spacing) * 0);
    }
  }
  .md\:pt-5 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 5);
    }
  }
  .md\:pt-6 {
    @media (width >= 48rem) {
      padding-top: calc(var(--spacing) * 6);
    }
  }
  .md\:pb-0 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .md\:pb-5 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 5);
    }
  }
  .md\:pb-6 {
    @media (width >= 48rem) {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }
  .md\:text-start {
    @media (width >= 48rem) {
      text-align: start;
    }
  }
  .md\:text-4xl {
    @media (width >= 48rem) {
      font-size: var(--text-4xl);
      line-height: var(--tw-leading, var(--text-4xl--line-height));
    }
  }
  .md\:text-5xl {
    @media (width >= 48rem) {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }
  .md\:text-base {
    @media (width >= 48rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .md\:text-xl {
    @media (width >= 48rem) {
      font-size: var(--text-xl);
      line-height: var(--tw-leading, var(--text-xl--line-height));
    }
  }
  .lg\:fixed {
    @media (width >= 64rem) {
      position: fixed;
    }
  }
  .lg\:bottom-0 {
    @media (width >= 64rem) {
      bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:z-0 {
    @media (width >= 64rem) {
      z-index: 0;
    }
  }
  .lg\:order-1 {
    @media (width >= 64rem) {
      order: 1;
    }
  }
  .lg\:order-2 {
    @media (width >= 64rem) {
      order: 2;
    }
  }
  .lg\:order-3 {
    @media (width >= 64rem) {
      order: 3;
    }
  }
  .lg\:col-span-3 {
    @media (width >= 64rem) {
      grid-column: span 3 / span 3;
    }
  }
  .lg\:col-span-5 {
    @media (width >= 64rem) {
      grid-column: span 5 / span 5;
    }
  }
  .lg\:col-span-6 {
    @media (width >= 64rem) {
      grid-column: span 6 / span 6;
    }
  }
  .lg\:col-span-7 {
    @media (width >= 64rem) {
      grid-column: span 7 / span 7;
    }
  }
  .lg\:mx-0 {
    @media (width >= 64rem) {
      margin-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:my-0 {
    @media (width >= 64rem) {
      margin-block: calc(var(--spacing) * 0);
    }
  }
  .lg\:my-8 {
    @media (width >= 64rem) {
      margin-block: calc(var(--spacing) * 8);
    }
  }
  .lg\:ms-64 {
    @media (width >= 64rem) {
      margin-inline-start: calc(var(--spacing) * 64);
    }
  }
  .lg\:mt-0 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:mt-6 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .lg\:mt-16 {
    @media (width >= 64rem) {
      margin-top: calc(var(--spacing) * 16);
    }
  }
  .lg\:mr-0 {
    @media (width >= 64rem) {
      margin-right: calc(var(--spacing) * 0);
    }
  }
  .lg\:mr-4 {
    @media (width >= 64rem) {
      margin-right: calc(var(--spacing) * 4);
    }
  }
  .lg\:mb-0 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:mb-4 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 4);
    }
  }
  .lg\:mb-6 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 6);
    }
  }
  .lg\:mb-10 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 10);
    }
  }
  .lg\:mb-12 {
    @media (width >= 64rem) {
      margin-bottom: calc(var(--spacing) * 12);
    }
  }
  .lg\:ml-16 {
    @media (width >= 64rem) {
      margin-left: calc(var(--spacing) * 16);
    }
  }
  .lg\:block {
    @media (width >= 64rem) {
      display: block;
    }
  }
  .lg\:flex {
    @media (width >= 64rem) {
      display: flex;
    }
  }
  .lg\:grid {
    @media (width >= 64rem) {
      display: grid;
    }
  }
  .lg\:hidden {
    @media (width >= 64rem) {
      display: none;
    }
  }
  .lg\:inline {
    @media (width >= 64rem) {
      display: inline;
    }
  }
  .lg\:inline-flex {
    @media (width >= 64rem) {
      display: inline-flex;
    }
  }
  .lg\:h-6 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 6);
    }
  }
  .lg\:h-40 {
    @media (width >= 64rem) {
      height: calc(var(--spacing) * 40);
    }
  }
  .lg\:h-\[calc\(100vh-8rem\)\] {
    @media (width >= 64rem) {
      height: calc(100vh - 8rem);
    }
  }
  .lg\:max-h-\[60rem\] {
    @media (width >= 64rem) {
      max-height: 60rem;
    }
  }
  .lg\:w-2\/3 {
    @media (width >= 64rem) {
      width: calc(2/3 * 100%);
    }
  }
  .lg\:w-6 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 6);
    }
  }
  .lg\:w-60 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 60);
    }
  }
  .lg\:w-96 {
    @media (width >= 64rem) {
      width: calc(var(--spacing) * 96);
    }
  }
  .lg\:w-auto {
    @media (width >= 64rem) {
      width: auto;
    }
  }
  .lg\:w-full {
    @media (width >= 64rem) {
      width: 100%;
    }
  }
  .lg\:max-w-7xl {
    @media (width >= 64rem) {
      max-width: var(--container-7xl);
    }
  }
  .lg\:max-w-none {
    @media (width >= 64rem) {
      max-width: none;
    }
  }
  .lg\:shrink-0 {
    @media (width >= 64rem) {
      flex-shrink: 0;
    }
  }
  .lg\:translate-x-0 {
    @media (width >= 64rem) {
      --tw-translate-x: calc(var(--spacing) * 0);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .lg\:grid-cols-1 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-2 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-3 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-5 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-6 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-12 {
    @media (width >= 64rem) {
      grid-template-columns: repeat(12, minmax(0, 1fr));
    }
  }
  .lg\:flex-row {
    @media (width >= 64rem) {
      flex-direction: row;
    }
  }
  .lg\:items-center {
    @media (width >= 64rem) {
      align-items: center;
    }
  }
  .lg\:justify-between {
    @media (width >= 64rem) {
      justify-content: space-between;
    }
  }
  .lg\:justify-end {
    @media (width >= 64rem) {
      justify-content: flex-end;
    }
  }
  .lg\:gap-8 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 8);
    }
  }
  .lg\:gap-12 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 12);
    }
  }
  .lg\:gap-16 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .lg\:gap-20 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 20);
    }
  }
  .lg\:gap-24 {
    @media (width >= 64rem) {
      gap: calc(var(--spacing) * 24);
    }
  }
  .lg\:space-y-0 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .lg\:space-y-6 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .lg\:gap-x-4 {
    @media (width >= 64rem) {
      column-gap: calc(var(--spacing) * 4);
    }
  }
  .lg\:space-x-4 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .lg\:space-x-6 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .lg\:space-x-8 {
    @media (width >= 64rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .lg\:gap-y-0 {
    @media (width >= 64rem) {
      row-gap: calc(var(--spacing) * 0);
    }
  }
  .lg\:rounded-lg {
    @media (width >= 64rem) {
      border-radius: var(--radius-lg);
    }
  }
  .lg\:border-0 {
    @media (width >= 64rem) {
      border-style: var(--tw-border-style);
      border-width: 0px;
    }
  }
  .lg\:border-b {
    @media (width >= 64rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 1px;
    }
  }
  .lg\:border-b-0 {
    @media (width >= 64rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .lg\:border-gray-200 {
    @media (width >= 64rem) {
      border-color: var(--color-gray-200);
    }
  }
  .lg\:bg-transparent {
    @media (width >= 64rem) {
      background-color: transparent;
    }
  }
  .lg\:p-0 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 0);
    }
  }
  .lg\:p-10 {
    @media (width >= 64rem) {
      padding: calc(var(--spacing) * 10);
    }
  }
  .lg\:px-0 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .lg\:px-6 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 6);
    }
  }
  .lg\:px-8 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 8);
    }
  }
  .lg\:px-12 {
    @media (width >= 64rem) {
      padding-inline: calc(var(--spacing) * 12);
    }
  }
  .lg\:py-0 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 0);
    }
  }
  .lg\:py-6 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 6);
    }
  }
  .lg\:py-16 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 16);
    }
  }
  .lg\:py-24 {
    @media (width >= 64rem) {
      padding-block: calc(var(--spacing) * 24);
    }
  }
  .lg\:ps-8 {
    @media (width >= 64rem) {
      padding-inline-start: calc(var(--spacing) * 8);
    }
  }
  .lg\:pt-0 {
    @media (width >= 64rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .lg\:pt-16 {
    @media (width >= 64rem) {
      padding-top: calc(var(--spacing) * 16);
    }
  }
  .lg\:pb-0 {
    @media (width >= 64rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .lg\:pb-6 {
    @media (width >= 64rem) {
      padding-bottom: calc(var(--spacing) * 6);
    }
  }
  .lg\:pb-8 {
    @media (width >= 64rem) {
      padding-bottom: calc(var(--spacing) * 8);
    }
  }
  .lg\:pl-2 {
    @media (width >= 64rem) {
      padding-left: calc(var(--spacing) * 2);
    }
  }
  .lg\:pl-14 {
    @media (width >= 64rem) {
      padding-left: calc(var(--spacing) * 14);
    }
  }
  .lg\:text-center {
    @media (width >= 64rem) {
      text-align: center;
    }
  }
  .lg\:text-right {
    @media (width >= 64rem) {
      text-align: right;
    }
  }
  .lg\:text-base {
    @media (width >= 64rem) {
      font-size: var(--text-base);
      line-height: var(--tw-leading, var(--text-base--line-height));
    }
  }
  .lg\:text-lg {
    @media (width >= 64rem) {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }
  .lg\:text-primary-700 {
    @media (width >= 64rem) {
      color: var(--color-primary-700);
    }
  }
  .lg\:hover\:bg-transparent {
    @media (width >= 64rem) {
      &:hover {
        @media (hover: hover) {
          background-color: transparent;
        }
      }
    }
  }
  .lg\:hover\:text-primary-700 {
    @media (width >= 64rem) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-700);
        }
      }
    }
  }
  .xl\:col-span-1 {
    @media (width >= 80rem) {
      grid-column: span 1 / span 1;
    }
  }
  .xl\:col-span-2 {
    @media (width >= 80rem) {
      grid-column: span 2 / span 2;
    }
  }
  .xl\:me-80 {
    @media (width >= 80rem) {
      margin-inline-end: calc(var(--spacing) * 80);
    }
  }
  .xl\:me-96 {
    @media (width >= 80rem) {
      margin-inline-end: calc(var(--spacing) * 96);
    }
  }
  .xl\:mt-6 {
    @media (width >= 80rem) {
      margin-top: calc(var(--spacing) * 6);
    }
  }
  .xl\:mr-0 {
    @media (width >= 80rem) {
      margin-right: calc(var(--spacing) * 0);
    }
  }
  .xl\:mb-0 {
    @media (width >= 80rem) {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }
  .xl\:block {
    @media (width >= 80rem) {
      display: block;
    }
  }
  .xl\:flex {
    @media (width >= 80rem) {
      display: flex;
    }
  }
  .xl\:grid {
    @media (width >= 80rem) {
      display: grid;
    }
  }
  .xl\:w-2\/3 {
    @media (width >= 80rem) {
      width: calc(2/3 * 100%);
    }
  }
  .xl\:max-w-4xl {
    @media (width >= 80rem) {
      max-width: var(--container-4xl);
    }
  }
  .xl\:max-w-lg {
    @media (width >= 80rem) {
      max-width: var(--container-lg);
    }
  }
  .xl\:max-w-screen-md {
    @media (width >= 80rem) {
      max-width: var(--breakpoint-md);
    }
  }
  .xl\:\!translate-x-0 {
    @media (width >= 80rem) {
      --tw-translate-x: calc(var(--spacing) * 0) !important;
      translate: var(--tw-translate-x) var(--tw-translate-y) !important;
    }
  }
  .xl\:grid-cols-2 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-3 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-4 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .xl\:grid-cols-6 {
    @media (width >= 80rem) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  .xl\:items-center {
    @media (width >= 80rem) {
      align-items: center;
    }
  }
  .xl\:justify-between {
    @media (width >= 80rem) {
      justify-content: space-between;
    }
  }
  .xl\:gap-4 {
    @media (width >= 80rem) {
      gap: calc(var(--spacing) * 4);
    }
  }
  .xl\:gap-10 {
    @media (width >= 80rem) {
      gap: calc(var(--spacing) * 10);
    }
  }
  .xl\:gap-16 {
    @media (width >= 80rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .xl\:space-y-3 {
    @media (width >= 80rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .xl\:space-x-0 {
    @media (width >= 80rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 0) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .xl\:space-x-3 {
    @media (width >= 80rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .xl\:border-e {
    @media (width >= 80rem) {
      border-inline-end-style: var(--tw-border-style);
      border-inline-end-width: 1px;
    }
  }
  .xl\:border-b-0 {
    @media (width >= 80rem) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 0px;
    }
  }
  .xl\:p-8 {
    @media (width >= 80rem) {
      padding: calc(var(--spacing) * 8);
    }
  }
  .xl\:px-0 {
    @media (width >= 80rem) {
      padding-inline: calc(var(--spacing) * 0);
    }
  }
  .xl\:ps-4 {
    @media (width >= 80rem) {
      padding-inline-start: calc(var(--spacing) * 4);
    }
  }
  .xl\:pt-0 {
    @media (width >= 80rem) {
      padding-top: calc(var(--spacing) * 0);
    }
  }
  .xl\:pb-0 {
    @media (width >= 80rem) {
      padding-bottom: calc(var(--spacing) * 0);
    }
  }
  .xl\:text-center {
    @media (width >= 80rem) {
      text-align: center;
    }
  }
  .\32 xl\:absolute {
    @media (width >= 96rem) {
      position: absolute;
    }
  }
  .\32 xl\:col-span-1 {
    @media (width >= 96rem) {
      grid-column: span 1 / span 1;
    }
  }
  .\32 xl\:col-span-2 {
    @media (width >= 96rem) {
      grid-column: span 2 / span 2;
    }
  }
  .\32 xl\:col-span-3 {
    @media (width >= 96rem) {
      grid-column: span 3 / span 3;
    }
  }
  .\32 xl\:col-span-4 {
    @media (width >= 96rem) {
      grid-column: span 4 / span 4;
    }
  }
  .\32 xl\:col-span-5 {
    @media (width >= 96rem) {
      grid-column: span 5 / span 5;
    }
  }
  .\32 xl\:col-span-7 {
    @media (width >= 96rem) {
      grid-column: span 7 / span 7;
    }
  }
  .\32 xl\:col-span-8 {
    @media (width >= 96rem) {
      grid-column: span 8 / span 8;
    }
  }
  .\32 xl\:col-span-9 {
    @media (width >= 96rem) {
      grid-column: span 9 / span 9;
    }
  }
  .\32 xl\:mt-8 {
    @media (width >= 96rem) {
      margin-top: calc(var(--spacing) * 8);
    }
  }
  .\32 xl\:flex {
    @media (width >= 96rem) {
      display: flex;
    }
  }
  .\32 xl\:max-h-fit {
    @media (width >= 96rem) {
      max-height: fit-content;
    }
  }
  .\32 xl\:max-w-screen-lg {
    @media (width >= 96rem) {
      max-width: var(--breakpoint-lg);
    }
  }
  .\32 xl\:grid-cols-1 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  .\32 xl\:grid-cols-2 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .\32 xl\:grid-cols-3 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .\32 xl\:grid-cols-4 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .\32 xl\:grid-cols-6 {
    @media (width >= 96rem) {
      grid-template-columns: repeat(6, minmax(0, 1fr));
    }
  }
  .\32 xl\:gap-16 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 16);
    }
  }
  .\32 xl\:gap-24 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 24);
    }
  }
  .\32 xl\:gap-60 {
    @media (width >= 96rem) {
      gap: calc(var(--spacing) * 60);
    }
  }
  .\32 xl\:space-y-0 {
    @media (width >= 96rem) {
      :where(& > :not(:last-child)) {
        --tw-space-y-reverse: 0;
        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));
        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));
      }
    }
  }
  .\32 xl\:space-x-4 {
    @media (width >= 96rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .\32 xl\:space-x-24 {
    @media (width >= 96rem) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 0;
        margin-inline-start: calc(calc(var(--spacing) * 24) * var(--tw-space-x-reverse));
        margin-inline-end: calc(calc(var(--spacing) * 24) * calc(1 - var(--tw-space-x-reverse)));
      }
    }
  }
  .\32 xl\:divide-x {
    @media (width >= 96rem) {
      :where(& > :not(:last-child)) {
        --tw-divide-x-reverse: 0;
        border-inline-style: var(--tw-border-style);
        border-inline-start-width: calc(1px * var(--tw-divide-x-reverse));
        border-inline-end-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
      }
    }
  }
  .\32 xl\:divide-gray-200 {
    @media (width >= 96rem) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-200);
      }
    }
  }
  .\32 xl\:p-6 {
    @media (width >= 96rem) {
      padding: calc(var(--spacing) * 6);
    }
  }
  .\32 xl\:ps-8 {
    @media (width >= 96rem) {
      padding-inline-start: calc(var(--spacing) * 8);
    }
  }
  .rtl\:translate-x-1\/2 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      --tw-translate-x: calc(1/2 * 100%);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }
  .rtl\:-rotate-90 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: calc(90deg * -1);
    }
  }
  .rtl\:rotate-180 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      rotate: 180deg;
    }
  }
  .rtl\:space-x-reverse {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      :where(& > :not(:last-child)) {
        --tw-space-x-reverse: 1;
      }
    }
  }
  .rtl\:text-right {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      text-align: right;
    }
  }
  .rtl\:peer-focus\:left-auto {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      &:is(:where(.peer):focus ~ *) {
        left: auto;
      }
    }
  }
  .rtl\:peer-focus\:translate-x-1\/4 {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      &:is(:where(.peer):focus ~ *) {
        --tw-translate-x: calc(1/4 * 100%);
        translate: var(--tw-translate-x) var(--tw-translate-y);
      }
    }
  }
  .rtl\:peer-checked\:after\:-translate-x-full {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      &:is(:where(.peer):checked ~ *) {
        &::after {
          content: var(--tw-content);
          --tw-translate-x: -100%;
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
  }
  .rtl\:peer-checked\:after\:translate-x-\[-100\%\] {
    &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
      &:is(:where(.peer):checked ~ *) {
        &::after {
          content: var(--tw-content);
          --tw-translate-x: -100%;
          translate: var(--tw-translate-x) var(--tw-translate-y);
        }
      }
    }
  }
  .sm\:rtl\:divide-x-reverse {
    @media (width >= 40rem) {
      &:where(:dir(rtl), [dir="rtl"], [dir="rtl"] *) {
        :where(& > :not(:last-child)) {
          --tw-divide-x-reverse: 1;
        }
      }
    }
  }
  .dark\:block {
    &:where(.dark, .dark *) {
      display: block;
    }
  }
  .dark\:flex {
    &:where(.dark, .dark *) {
      display: flex;
    }
  }
  .dark\:hidden {
    &:where(.dark, .dark *) {
      display: none;
    }
  }
  .dark\:divide-gray-600 {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-600);
      }
    }
  }
  .dark\:divide-gray-700 {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-700);
      }
    }
  }
  .dark\:divide-gray-800 {
    &:where(.dark, .dark *) {
      :where(& > :not(:last-child)) {
        border-color: var(--color-gray-800);
      }
    }
  }
  .dark\:\!border-gray-600 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-600) !important;
    }
  }
  .dark\:\!border-gray-700 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-700) !important;
    }
  }
  .dark\:\!border-gray-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-800) !important;
    }
  }
  .dark\:border-blue-500 {
    &:where(.dark, .dark *) {
      border-color: var(--color-blue-500);
    }
  }
  .dark\:border-gray-500 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-500);
    }
  }
  .dark\:border-gray-600 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-600);
    }
  }
  .dark\:border-gray-700 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-700);
    }
  }
  .dark\:border-gray-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-800);
    }
  }
  .dark\:border-gray-900 {
    &:where(.dark, .dark *) {
      border-color: var(--color-gray-900);
    }
  }
  .dark\:border-green-800 {
    &:where(.dark, .dark *) {
      border-color: var(--color-green-800);
    }
  }
  .dark\:border-primary-500 {
    &:where(.dark, .dark *) {
      border-color: var(--color-primary-500);
    }
  }
  .dark\:border-primary-600 {
    &:where(.dark, .dark *) {
      border-color: var(--color-primary-600);
    }
  }
  .dark\:border-red-500 {
    &:where(.dark, .dark *) {
      border-color: var(--color-red-500);
    }
  }
  .dark\:border-transparent {
    &:where(.dark, .dark *) {
      border-color: transparent;
    }
  }
  .dark\:border-s-gray-700 {
    &:where(.dark, .dark *) {
      border-inline-start-color: var(--color-gray-700);
    }
  }
  .dark\:\!bg-gray-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-600) !important;
    }
  }
  .dark\:\!bg-gray-700 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-700) !important;
    }
  }
  .dark\:\!bg-gray-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-800) !important;
    }
  }
  .dark\:\!bg-green-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-green-900) !important;
    }
  }
  .dark\:\!bg-indigo-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-indigo-900) !important;
    }
  }
  .dark\:\!bg-orange-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-orange-300) !important;
    }
  }
  .dark\:\!bg-orange-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-orange-900) !important;
    }
  }
  .dark\:\!bg-pink-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-pink-900) !important;
    }
  }
  .dark\:\!bg-primary-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-600) !important;
    }
  }
  .dark\:\!bg-primary-700 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-700) !important;
    }
  }
  .dark\:\!bg-primary-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-900) !important;
    }
  }
  .dark\:\!bg-purple-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-purple-900) !important;
    }
  }
  .dark\:\!bg-red-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-900) !important;
    }
  }
  .dark\:\!bg-teal-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-teal-900) !important;
    }
  }
  .dark\:\!bg-yellow-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-yellow-900) !important;
    }
  }
  .dark\:bg-blue-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-blue-600);
    }
  }
  .dark\:bg-gray-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-500);
    }
  }
  .dark\:bg-gray-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-600);
    }
  }
  .dark\:bg-gray-600\/40 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, #4B5563 40%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-600) 40%, transparent);
      }
    }
  }
  .dark\:bg-gray-700 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-700);
    }
  }
  .dark\:bg-gray-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-800);
    }
  }
  .dark\:bg-gray-800\/30 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, #1F2937 30%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-800) 30%, transparent);
      }
    }
  }
  .dark\:bg-gray-800\/40 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, #1F2937 40%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-800) 40%, transparent);
      }
    }
  }
  .dark\:bg-gray-800\/50 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, #1F2937 50%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-800) 50%, transparent);
      }
    }
  }
  .dark\:bg-gray-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-gray-900);
    }
  }
  .dark\:bg-gray-900\/80 {
    &:where(.dark, .dark *) {
      background-color: color-mix(in srgb, #111827 80%, transparent);
      @supports (color: color-mix(in lab, red, red)) {
        background-color: color-mix(in oklab, var(--color-gray-900) 80%, transparent);
      }
    }
  }
  .dark\:bg-green-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-green-300);
    }
  }
  .dark\:bg-green-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-green-500);
    }
  }
  .dark\:bg-green-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-green-900);
    }
  }
  .dark\:bg-indigo-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-indigo-300);
    }
  }
  .dark\:bg-indigo-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-indigo-900);
    }
  }
  .dark\:bg-orange-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-orange-300);
    }
  }
  .dark\:bg-orange-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-orange-900);
    }
  }
  .dark\:bg-pink-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-pink-300);
    }
  }
  .dark\:bg-pink-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-pink-900);
    }
  }
  .dark\:bg-primary-200 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-200);
    }
  }
  .dark\:bg-primary-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-300);
    }
  }
  .dark\:bg-primary-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-500);
    }
  }
  .dark\:bg-primary-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-600);
    }
  }
  .dark\:bg-primary-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-800);
    }
  }
  .dark\:bg-primary-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-primary-900);
    }
  }
  .dark\:bg-purple-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-purple-300);
    }
  }
  .dark\:bg-purple-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-purple-800);
    }
  }
  .dark\:bg-purple-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-purple-900);
    }
  }
  .dark\:bg-red-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-300);
    }
  }
  .dark\:bg-red-500 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-500);
    }
  }
  .dark\:bg-red-600 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-600);
    }
  }
  .dark\:bg-red-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-red-900);
    }
  }
  .dark\:bg-teal-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-teal-300);
    }
  }
  .dark\:bg-teal-800 {
    &:where(.dark, .dark *) {
      background-color: var(--color-teal-800);
    }
  }
  .dark\:bg-teal-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-teal-900);
    }
  }
  .dark\:bg-yellow-300 {
    &:where(.dark, .dark *) {
      background-color: var(--color-yellow-300);
    }
  }
  .dark\:bg-yellow-900 {
    &:where(.dark, .dark *) {
      background-color: var(--color-yellow-900);
    }
  }
  .dark\:bg-\[url\(\'https\:\/\/flowbite\.s3\.amazonaws\.com\/blocks\/e-commerce\/gaming-image\.jpg\'\)\] {
    &:where(.dark, .dark *) {
      background-image: url('https://flowbite.s3.amazonaws.com/blocks/e-commerce/gaming-image.jpg');
    }
  }
  .dark\:fill-gray-400 {
    &:where(.dark, .dark *) {
      fill: var(--color-gray-400);
    }
  }
  .dark\:fill-gray-500 {
    &:where(.dark, .dark *) {
      fill: var(--color-gray-500);
    }
  }
  .dark\:fill-white {
    &:where(.dark, .dark *) {
      fill: var(--color-white);
    }
  }
  .dark\:\!text-gray-400 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-400) !important;
    }
  }
  .dark\:\!text-green-300 {
    &:where(.dark, .dark *) {
      color: var(--color-green-300) !important;
    }
  }
  .dark\:\!text-indigo-300 {
    &:where(.dark, .dark *) {
      color: var(--color-indigo-300) !important;
    }
  }
  .dark\:\!text-orange-300 {
    &:where(.dark, .dark *) {
      color: var(--color-orange-300) !important;
    }
  }
  .dark\:\!text-pink-300 {
    &:where(.dark, .dark *) {
      color: var(--color-pink-300) !important;
    }
  }
  .dark\:\!text-primary-300 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-300) !important;
    }
  }
  .dark\:\!text-purple-300 {
    &:where(.dark, .dark *) {
      color: var(--color-purple-300) !important;
    }
  }
  .dark\:\!text-red-300 {
    &:where(.dark, .dark *) {
      color: var(--color-red-300) !important;
    }
  }
  .dark\:\!text-teal-300 {
    &:where(.dark, .dark *) {
      color: var(--color-teal-300) !important;
    }
  }
  .dark\:\!text-white {
    &:where(.dark, .dark *) {
      color: var(--color-white) !important;
    }
  }
  .dark\:\!text-yellow-300 {
    &:where(.dark, .dark *) {
      color: var(--color-yellow-300) !important;
    }
  }
  .dark\:text-blue-500 {
    &:where(.dark, .dark *) {
      color: var(--color-blue-500);
    }
  }
  .dark\:text-gray-100 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-100);
    }
  }
  .dark\:text-gray-200 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-200);
    }
  }
  .dark\:text-gray-300 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-300);
    }
  }
  .dark\:text-gray-400 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-400);
    }
  }
  .dark\:text-gray-500 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-500);
    }
  }
  .dark\:text-gray-700 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-700);
    }
  }
  .dark\:text-gray-800 {
    &:where(.dark, .dark *) {
      color: var(--color-gray-800);
    }
  }
  .dark\:text-green-300 {
    &:where(.dark, .dark *) {
      color: var(--color-green-300);
    }
  }
  .dark\:text-green-400 {
    &:where(.dark, .dark *) {
      color: var(--color-green-400);
    }
  }
  .dark\:text-green-500 {
    &:where(.dark, .dark *) {
      color: var(--color-green-500);
    }
  }
  .dark\:text-indigo-300 {
    &:where(.dark, .dark *) {
      color: var(--color-indigo-300);
    }
  }
  .dark\:text-indigo-400 {
    &:where(.dark, .dark *) {
      color: var(--color-indigo-400);
    }
  }
  .dark\:text-orange-300 {
    &:where(.dark, .dark *) {
      color: var(--color-orange-300);
    }
  }
  .dark\:text-pink-300 {
    &:where(.dark, .dark *) {
      color: var(--color-pink-300);
    }
  }
  .dark\:text-pink-400 {
    &:where(.dark, .dark *) {
      color: var(--color-pink-400);
    }
  }
  .dark\:text-primary-300 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-300);
    }
  }
  .dark\:text-primary-400 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-400);
    }
  }
  .dark\:text-primary-500 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-500);
    }
  }
  .dark\:text-primary-600 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-600);
    }
  }
  .dark\:text-primary-800 {
    &:where(.dark, .dark *) {
      color: var(--color-primary-800);
    }
  }
  .dark\:text-purple-300 {
    &:where(.dark, .dark *) {
      color: var(--color-purple-300);
    }
  }
  .dark\:text-purple-400 {
    &:where(.dark, .dark *) {
      color: var(--color-purple-400);
    }
  }
  .dark\:text-red-300 {
    &:where(.dark, .dark *) {
      color: var(--color-red-300);
    }
  }
  .dark\:text-red-400 {
    &:where(.dark, .dark *) {
      color: var(--color-red-400);
    }
  }
  .dark\:text-red-500 {
    &:where(.dark, .dark *) {
      color: var(--color-red-500);
    }
  }
  .dark\:text-teal-300 {
    &:where(.dark, .dark *) {
      color: var(--color-teal-300);
    }
  }
  .dark\:text-teal-400 {
    &:where(.dark, .dark *) {
      color: var(--color-teal-400);
    }
  }
  .dark\:text-white {
    &:where(.dark, .dark *) {
      color: var(--color-white);
    }
  }
  .dark\:text-yellow-300 {
    &:where(.dark, .dark *) {
      color: var(--color-yellow-300);
    }
  }
  .dark\:text-yellow-400 {
    &:where(.dark, .dark *) {
      color: var(--color-yellow-400);
    }
  }
  .dark\:placeholder-gray-400 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: var(--color-gray-400);
      }
    }
  }
  .dark\:ring-gray-800 {
    &:where(.dark, .dark *) {
      --tw-ring-color: var(--color-gray-800);
    }
  }
  .dark\:ring-offset-gray-700 {
    &:where(.dark, .dark *) {
      --tw-ring-offset-color: var(--color-gray-700);
    }
  }
  .dark\:ring-offset-gray-800 {
    &:where(.dark, .dark *) {
      --tw-ring-offset-color: var(--color-gray-800);
    }
  }
  .dark\:invert {
    &:where(.dark, .dark *) {
      --tw-invert: invert(100%);
      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);
    }
  }
  .dark\:format-invert {
    &:where(.dark, .dark *) {
      --tw-format-body: var(--tw-format-invert-body);
      --tw-format-headings: var(--tw-format-invert-headings);
      --tw-format-lead: var(--tw-format-invert-lead);
      --tw-format-links: var(--tw-format-invert-links);
      --tw-format-bold: var(--tw-format-invert-bold);
      --tw-format-counters: var(--tw-format-invert-counters);
      --tw-format-bullets: var(--tw-format-invert-bullets);
      --tw-format-hr: var(--tw-format-invert-hr);
      --tw-format-quotes: var(--tw-format-invert-quotes);
      --tw-format-quote-borders: var(--tw-format-invert-quote-borders);
      --tw-format-captions: var(--tw-format-invert-captions);
      --tw-format-code: var(--tw-format-invert-code);
      --tw-format-code-bg: var(--tw-format-invert-code-bg);
      --tw-format-pre-code: var(--tw-format-invert-pre-code);
      --tw-format-pre-bg: var(--tw-format-invert-pre-bg);
      --tw-format-th-borders: var(--tw-format-invert-th-borders);
      --tw-format-td-borders: var(--tw-format-invert-td-borders);
      --tw-format-th-bg: var(--tw-format-invert-th-bg);
    }
  }
  .dark\:group-hover\:bg-gray-700 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: var(--color-gray-700);
        }
      }
    }
  }
  .dark\:group-hover\:bg-gray-800\/60 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: color-mix(in srgb, #1F2937 60%, transparent);
          @supports (color: color-mix(in lab, red, red)) {
            background-color: color-mix(in oklab, var(--color-gray-800) 60%, transparent);
          }
        }
      }
    }
  }
  .dark\:group-hover\:bg-primary-700 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: var(--color-primary-700);
        }
      }
    }
  }
  .dark\:group-hover\:bg-purple-700 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: var(--color-purple-700);
        }
      }
    }
  }
  .dark\:group-hover\:bg-teal-700 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          background-color: var(--color-teal-700);
        }
      }
    }
  }
  .dark\:group-hover\:text-gray-400 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          color: var(--color-gray-400);
        }
      }
    }
  }
  .dark\:group-hover\:text-white {
    &:where(.dark, .dark *) {
      &:is(:where(.group):hover *) {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
  .dark\:group-focus\:ring-gray-800\/70 {
    &:where(.dark, .dark *) {
      &:is(:where(.group):focus *) {
        --tw-ring-color: color-mix(in srgb, #1F2937 70%, transparent);
        @supports (color: color-mix(in lab, red, red)) {
          --tw-ring-color: color-mix(in oklab, var(--color-gray-800) 70%, transparent);
        }
      }
    }
  }
  .dark\:peer-checked\:border-primary-500 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        border-color: var(--color-primary-500);
      }
    }
  }
  .dark\:peer-checked\:border-primary-600 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        border-color: var(--color-primary-600);
      }
    }
  }
  .dark\:peer-checked\:bg-primary-500 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        background-color: var(--color-primary-500);
      }
    }
  }
  .dark\:peer-checked\:bg-primary-600 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):checked ~ *) {
        background-color: var(--color-primary-600);
      }
    }
  }
  .peer-focus\:dark\:text-primary-500 {
    &:is(:where(.peer):focus ~ *) {
      &:where(.dark, .dark *) {
        color: var(--color-primary-500);
      }
    }
  }
  .dark\:peer-focus\:ring-primary-800 {
    &:where(.dark, .dark *) {
      &:is(:where(.peer):focus ~ *) {
        --tw-ring-color: var(--color-primary-800);
      }
    }
  }
  .dark\:placeholder\:text-gray-400 {
    &:where(.dark, .dark *) {
      &::placeholder {
        color: var(--color-gray-400);
      }
    }
  }
  .dark\:hover\:border-gray-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--color-gray-500);
        }
      }
    }
  }
  .dark\:hover\:border-gray-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--color-gray-600);
        }
      }
    }
  }
  .dark\:hover\:border-primary-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--color-primary-500);
        }
      }
    }
  }
  .dark\:hover\:border-primary-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--color-primary-600);
        }
      }
    }
  }
  .dark\:hover\:border-primary-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          border-color: var(--color-primary-700);
        }
      }
    }
  }
  .dark\:hover\:\!bg-gray-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-700) !important;
        }
      }
    }
  }
  .dark\:hover\:\!bg-primary-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-700) !important;
        }
      }
    }
  }
  .dark\:hover\:bg-blue-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-blue-700);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-500);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-600);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-700);
        }
      }
    }
  }
  .dark\:hover\:bg-gray-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-gray-800);
        }
      }
    }
  }
  .dark\:hover\:bg-green-400 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-green-400);
        }
      }
    }
  }
  .dark\:hover\:bg-green-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-green-800);
        }
      }
    }
  }
  .dark\:hover\:bg-indigo-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-indigo-800);
        }
      }
    }
  }
  .dark\:hover\:bg-orange-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-orange-800);
        }
      }
    }
  }
  .dark\:hover\:bg-pink-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-pink-800);
        }
      }
    }
  }
  .dark\:hover\:bg-primary-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-500);
        }
      }
    }
  }
  .dark\:hover\:bg-primary-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-600);
        }
      }
    }
  }
  .dark\:hover\:bg-primary-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-700);
        }
      }
    }
  }
  .dark\:hover\:bg-primary-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-800);
        }
      }
    }
  }
  .dark\:hover\:bg-primary-900 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-primary-900);
        }
      }
    }
  }
  .dark\:hover\:bg-purple-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-purple-800);
        }
      }
    }
  }
  .dark\:hover\:bg-red-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-600);
        }
      }
    }
  }
  .dark\:hover\:bg-red-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-700);
        }
      }
    }
  }
  .dark\:hover\:bg-red-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-red-800);
        }
      }
    }
  }
  .dark\:hover\:bg-teal-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-teal-800);
        }
      }
    }
  }
  .dark\:hover\:bg-yellow-800 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-yellow-800);
        }
      }
    }
  }
  .dark\:hover\:\!text-white {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white) !important;
        }
      }
    }
  }
  .dark\:hover\:text-blue-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-blue-500);
        }
      }
    }
  }
  .dark\:hover\:text-gray-100 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-100);
        }
      }
    }
  }
  .dark\:hover\:text-gray-300 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-gray-300);
        }
      }
    }
  }
  .dark\:hover\:text-primary-100 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-100);
        }
      }
    }
  }
  .dark\:hover\:text-primary-300 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-300);
        }
      }
    }
  }
  .dark\:hover\:text-primary-500 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-500);
        }
      }
    }
  }
  .dark\:hover\:text-primary-600 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-600);
        }
      }
    }
  }
  .dark\:hover\:text-primary-700 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-primary-700);
        }
      }
    }
  }
  .dark\:hover\:text-red-400 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-red-400);
        }
      }
    }
  }
  .dark\:hover\:text-white {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-white);
        }
      }
    }
  }
  .dark\:hover\:text-yellow-400 {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          color: var(--color-yellow-400);
        }
      }
    }
  }
  .dark\:hover\:underline {
    &:where(.dark, .dark *) {
      &:hover {
        @media (hover: hover) {
          text-decoration-line: underline;
        }
      }
    }
  }
  .dark\:focus\:border-primary-500 {
    &:where(.dark, .dark *) {
      &:focus {
        border-color: var(--color-primary-500);
      }
    }
  }
  .dark\:focus\:text-white {
    &:where(.dark, .dark *) {
      &:focus {
        color: var(--color-white);
      }
    }
  }
  .dark\:focus\:\!ring-primary-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-800) !important;
      }
    }
  }
  .dark\:focus\:ring-\[\#4285F4\]\/55 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: color-mix(in oklab, #4285F4 55%, transparent);
      }
    }
  }
  .dark\:focus\:ring-blue-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-blue-800);
      }
    }
  }
  .dark\:focus\:ring-gray-600 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-gray-600);
      }
    }
  }
  .dark\:focus\:ring-gray-700 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-gray-700);
      }
    }
  }
  .dark\:focus\:ring-gray-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-gray-800);
      }
    }
  }
  .dark\:focus\:ring-green-700 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-green-700);
      }
    }
  }
  .dark\:focus\:ring-primary-500 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-500);
      }
    }
  }
  .dark\:focus\:ring-primary-600 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-600);
      }
    }
  }
  .dark\:focus\:ring-primary-700 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-700);
      }
    }
  }
  .dark\:focus\:ring-primary-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-800);
      }
    }
  }
  .dark\:focus\:ring-primary-900 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-primary-900);
      }
    }
  }
  .dark\:focus\:ring-red-800 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-red-800);
      }
    }
  }
  .dark\:focus\:ring-red-900 {
    &:where(.dark, .dark *) {
      &:focus {
        --tw-ring-color: var(--color-red-900);
      }
    }
  }
  .dark\:active\:\!bg-primary-700 {
    &:where(.dark, .dark *) {
      &:active {
        background-color: var(--color-primary-700) !important;
      }
    }
  }
  .dark\:active\:bg-primary-700 {
    &:where(.dark, .dark *) {
      &:active {
        background-color: var(--color-primary-700);
      }
    }
  }
  .sm\:dark\:border-gray-700 {
    @media (width >= 40rem) {
      &:where(.dark, .dark *) {
        border-color: var(--color-gray-700);
      }
    }
  }
  .lg\:dark\:hover\:bg-transparent {
    @media (width >= 64rem) {
      &:where(.dark, .dark *) {
        &:hover {
          @media (hover: hover) {
            background-color: transparent;
          }
        }
      }
    }
  }
  .lg\:dark\:hover\:text-white {
    @media (width >= 64rem) {
      &:where(.dark, .dark *) {
        &:hover {
          @media (hover: hover) {
            color: var(--color-white);
          }
        }
      }
    }
  }
  .\[\&\:\:-webkit-inner-spin-button\]\:appearance-none {
    &::-webkit-inner-spin-button {
      appearance: none;
    }
  }
  .\[\&\:\:-webkit-outer-spin-button\]\:appearance-none {
    &::-webkit-outer-spin-button {
      appearance: none;
    }
  }
}
.svgMap-map-wrapper {
  background-color: var(--color-white) !important;
}
.svgMap-map-image {
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800);
  }
}
.svgMap-map-controls-wrapper {
  bottom: calc(var(--spacing) * 0) !important;
  left: calc(var(--spacing) * 0) !important;
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
}
.svgMap-map-controls-zoom {
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
}
.svgMap-map-wrapper .svgMap-control-button {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 1px !important;
  --tw-border-style: solid !important;
  border-style: solid !important;
  border-color: var(--color-gray-300) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-600) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-600) !important;
      }
    }
  }
}
.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:after,
.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before {
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-600) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-500) !important;
      }
    }
  }
}
.svgMap-map-wrapper .svgMap-control-button:first-child {
  margin-right: calc(var(--spacing) * 2) !important;
}
.svgMap-tooltip {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-white) !important;
  text-align: left !important;
  --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1)) !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-700) !important;
  }
}
.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container {
  margin-right: calc(var(--spacing) * 2) !important;
  display: inline-block !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  padding: calc(var(--spacing) * 0) !important;
  text-align: left !important;
}
.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container .svgMap-tooltip-flag {
  display: inline-block !important;
  height: calc(var(--spacing) * 4) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  padding: calc(var(--spacing) * 0) !important;
}
.svgMap-tooltip .svgMap-tooltip-title {
  display: inline-block !important;
  padding-top: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-gray-900) !important;
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.svgMap-tooltip .svgMap-tooltip-content {
  margin-top: calc(var(--spacing) * 0) !important;
}
.svgMap-tooltip .svgMap-tooltip-content table td {
  text-align: left !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-normal) !important;
  font-weight: var(--font-weight-normal) !important;
  color: var(--color-gray-500) !important;
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
}
.svgMap-tooltip .svgMap-tooltip-content table td span {
  text-align: left !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-gray-900) !important;
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.svgMap-tooltip .svgMap-tooltip-pointer {
  display: none !important;
}
.svgMap-map-wrapper .svgMap-country {
  &:where(.dark, .dark *) {
    stroke: var(--color-gray-800) !important;
  }
}
.drag-card {
  rotate: 6deg !important;
  opacity: 100% !important;
}
.ghost-card {
  background-color: color-mix(in srgb, #F3F4F6 40%, transparent) !important;
  @supports (color: color-mix(in lab, red, red)) {
    background-color: color-mix(in oklab, var(--color-gray-100) 40%, transparent) !important;
  }
  &:where(.dark, .dark *) {
    background-color: color-mix(in srgb, #4B5563 40%, transparent) !important;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-600) 40%, transparent) !important;
    }
  }
}
.fc .fc-toolbar {
  padding-inline: calc(var(--spacing) * 4) !important;
}
.fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: calc(var(--spacing) * 0) !important;
  flex-direction: column !important;
  :where(& > :not(:last-child)) {
    --tw-space-y-reverse: 0 !important;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse)) !important;
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse))) !important;
  }
  padding-top: calc(var(--spacing) * 4) !important;
  padding-bottom: calc(var(--spacing) * 4) !important;
  @media (width >= 48rem) {
    flex-direction: row !important;
  }
  @media (width >= 48rem) {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
}
.fc .fc-toolbar-title {
  flex-shrink: 0 !important;
  font-size: var(--text-lg) !important;
  line-height: var(--tw-leading, var(--text-lg--line-height)) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-gray-900) !important;
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.fc .fc-today-button {
  margin-right: calc(var(--spacing) * 0) !important;
  margin-left: calc(var(--spacing) * 4) !important;
  cursor: pointer !important;
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 1px !important;
  border-color: var(--color-gray-200) !important;
  background-color: var(--color-white) !important;
  padding-inline: calc(var(--spacing) * 3) !important;
  padding-block: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-900) !important;
  opacity: 100% !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-100) !important;
    }
  }
  &:hover {
    @media (hover: hover) {
      color: var(--color-primary-700) !important;
    }
  }
  &:focus {
    z-index: 10 !important;
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  &:focus {
    --tw-ring-color: var(--color-gray-100) !important;
  }
  &:focus {
    --tw-outline-style: none !important;
    outline-style: none !important;
  }
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-600) !important;
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700) !important;
      }
    }
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white) !important;
      }
    }
  }
  &:where(.dark, .dark *) {
    &:focus {
      --tw-ring-color: var(--color-gray-700) !important;
    }
  }
}
.fc .fc-button-group .fc-button-primary {
  border-style: var(--tw-border-style) !important;
  border-width: 1px !important;
  border-color: var(--color-gray-200) !important;
  background-color: var(--color-white) !important;
  padding-inline: calc(var(--spacing) * 3) !important;
  padding-block: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-gray-900) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-100) !important;
    }
  }
  &:hover {
    @media (hover: hover) {
      color: var(--color-primary-700) !important;
    }
  }
  &:focus {
    z-index: 10 !important;
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  &:focus {
    --tw-ring-color: var(--color-gray-100) !important;
  }
  &:focus {
    --tw-outline-style: none !important;
    outline-style: none !important;
  }
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-600) !important;
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-700) !important;
      }
    }
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        color: var(--color-white) !important;
      }
    }
  }
  &:where(.dark, .dark *) {
    &:focus {
      --tw-ring-color: var(--color-gray-700) !important;
    }
  }
}
.fc .fc-button-group .fc-button-primary:first-child {
  border-top-left-radius: var(--radius-lg) !important;
  border-bottom-left-radius: var(--radius-lg) !important;
}
.fc .fc-button-group .fc-button-primary:last-child {
  border-top-right-radius: var(--radius-lg) !important;
  border-bottom-right-radius: var(--radius-lg) !important;
}
.fc .fc-button-group .fc-prev-button,
.fc .fc-button-group .fc-next-button {
  display: inline-flex !important;
  max-width: calc(var(--spacing) * 12) !important;
  cursor: pointer !important;
  justify-content: center !important;
  border-radius: var(--radius-sm) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-white) !important;
  padding: calc(var(--spacing) * 2) !important;
  color: var(--color-gray-500) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-100) !important;
    }
  }
  &:hover {
    @media (hover: hover) {
      color: var(--color-gray-900) !important;
    }
  }
  &:focus {
    background-color: var(--color-gray-100) !important;
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  &:focus {
    --tw-ring-color: var(--color-gray-100) !important;
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
}
.fc .fc-scrollgrid {
  border-left-style: var(--tw-border-style) !important;
  border-left-width: 0px !important;
  border-color: var(--color-gray-200) !important;
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-800) !important;
  }
}
.fc .fc-daygrid-day-frame {
  border-color: var(--color-gray-200) !important;
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-800) !important;
  }
}
.fc .fc-col-header-cell-cushion {
  padding-block: calc(var(--spacing) * 3) !important;
  font-size: var(--text-base) !important;
  line-height: var(--tw-leading, var(--text-base--line-height)) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--color-gray-900) !important;
}
.fc-theme-standard th {
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  border-bottom-style: var(--tw-border-style) !important;
  border-bottom-width: 1px !important;
  border-color: var(--color-gray-200) !important;
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-800) !important;
  }
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end {
  margin-right: calc(var(--spacing) * 2) !important;
}
.fc-direction-ltr .fc-daygrid-event.fc-event-start {
  margin-left: calc(var(--spacing) * 2) !important;
}
.fc .fc-event .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-primary-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-primary-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-primary-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-primary-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-primary-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-800) !important;
      }
    }
  }
}
.fc .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-primary-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-primary-300) !important;
  }
}
.fc .fc-event.fc-event-purple .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-purple-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-purple-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-purple-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-purple-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-purple-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-purple-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-purple .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-purple-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-purple-300) !important;
  }
}
.fc .fc-event.fc-event-indigo .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-indigo-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-indigo-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-indigo-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-indigo-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-indigo-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-indigo-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-indigo .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-indigo-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-indigo-300) !important;
  }
}
.fc .fc-event.fc-event-pink .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-pink-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-pink-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-pink-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-pink-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-pink-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-pink-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-pink .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-pink-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-pink-300) !important;
  }
}
.fc .fc-event.fc-event-teal .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-teal-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-teal-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-teal-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-teal-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-teal-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-teal-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-teal .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-teal-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-teal-300) !important;
  }
}
.fc .fc-event.fc-event-green .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-green-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-green-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-green-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-green-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-green-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-green-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-green .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-green-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-green-300) !important;
  }
}
.fc .fc-event.fc-event-yellow .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-yellow-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-yellow-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-yellow-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-yellow-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-yellow-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-yellow-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-yellow .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-yellow-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-yellow-300) !important;
  }
}
.fc .fc-event.fc-event-orange .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-orange-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-orange-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-orange-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-orange-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-orange-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-orange-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-orange .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-orange-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-orange-300) !important;
  }
}
.fc .fc-event.fc-event-red .fc-event-main {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-red-50) !important;
  padding: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-red-700) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-red-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-red-900) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-red-300) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-red-800) !important;
      }
    }
  }
}
.fc .fc-event.fc-event-red .fc-event-main-frame:before {
  content: "";
  margin-right: calc(var(--spacing) * 1.5) !important;
  height: calc(var(--spacing) * 2) !important;
  width: calc(var(--spacing) * 2) !important;
  border-radius: calc(infinity * 1px) !important;
  background-color: var(--color-red-700) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-red-300) !important;
  }
}
.fc .fc-event {
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: transparent !important;
}
.fc .fc-h-event .fc-event-main-frame {
  font-size: var(--text-xs) !important;
  line-height: var(--tw-leading, var(--text-xs--line-height)) !important;
  --tw-font-weight: var(--font-weight-semibold) !important;
  font-weight: var(--font-weight-semibold) !important;
}
.fc .fc-daygrid-day-frame:hover {
  background-color: var(--color-gray-50) !important;
}
.fc .fc-daygrid-day-frame {
  cursor: pointer !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-50) !important;
    }
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-gray-800) !important;
      }
    }
  }
}
.fc .fc-addEventButton-button {
  margin-inline: calc(var(--spacing) * 0) !important;
  display: inline-flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: var(--radius-lg) !important;
  border-style: var(--tw-border-style) !important;
  border-width: 0px !important;
  background-color: var(--color-primary-700) !important;
  padding-inline: calc(var(--spacing) * 3) !important;
  padding-block: calc(var(--spacing) * 2) !important;
  font-size: var(--text-sm) !important;
  line-height: var(--tw-leading, var(--text-sm--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--color-white) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-primary-800) !important;
    }
  }
  &:focus {
    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor) !important;
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
  }
  &:focus {
    --tw-ring-color: var(--color-primary-300) !important;
  }
  &:focus {
    --tw-outline-style: none !important;
    outline-style: none !important;
  }
  &:active {
    background-color: var(--color-primary-800) !important;
  }
  @media (width >= 40rem) {
    margin-left: calc(var(--spacing) * 4) !important;
  }
  @media (width >= 40rem) {
    width: auto !important;
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-primary-600) !important;
  }
  &:where(.dark, .dark *) {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-primary-700) !important;
      }
    }
  }
  &:where(.dark, .dark *) {
    &:focus {
      --tw-ring-color: var(--color-primary-800) !important;
    }
  }
  &:where(.dark, .dark *) {
    &:active {
      background-color: var(--color-primary-700) !important;
    }
  }
}
.fc .fc-toolbar-chunk {
  display: flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: center !important;
  @media (width >= 40rem) {
    width: auto !important;
  }
  @media (width >= 40rem) {
    justify-content: flex-start !important;
  }
}
.fc .fc-toolbar-chunk:last-child {
  flex-direction: column !important;
  :where(& > :not(:last-child)) {
    --tw-space-y-reverse: 0 !important;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse)) !important;
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse))) !important;
  }
  @media (width >= 40rem) {
    flex-direction: row !important;
  }
  @media (width >= 40rem) {
    :where(& > :not(:last-child)) {
      --tw-space-y-reverse: 0 !important;
      margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse)) !important;
      margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse))) !important;
    }
  }
}
.fc .fc-toolbar-chunk > .fc-button-group {
  width: 100% !important;
  @media (width >= 40rem) {
    width: auto !important;
  }
}
.fc-theme-standard td,
.fc-theme-standard th {
  border-color: var(--color-gray-200) !important;
  &:where(.dark, .dark *) {
    border-color: var(--color-gray-800) !important;
  }
}
.fc .fc-daygrid-day-number {
  font-size: var(--text-base) !important;
  line-height: var(--tw-leading, var(--text-base--line-height)) !important;
  --tw-font-weight: var(--font-weight-medium) !important;
  font-weight: var(--font-weight-medium) !important;
}
.fc .fc-daygrid-day-number,
.fc .fc-col-header-cell-cushion {
  color: var(--color-gray-900) !important;
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.fc .fc-daygrid-day-top {
  display: flex !important;
  justify-content: center !important;
}
.fc .fc-daygrid-day.fc-day-today {
  background-color: var(--color-gray-50) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
}
.fc .fc-daygrid-event-harness,
.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs {
  margin-bottom: calc(var(--spacing) * 2) !important;
}
.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs {
  margin-left: calc(var(--spacing) * 0) !important;
  padding-inline: calc(var(--spacing) * 2) !important;
  padding-bottom: calc(var(--spacing) * 2) !important;
}
.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs .fc-daygrid-event.fc-event-start {
  margin-left: calc(var(--spacing) * 0) !important;
}
.fc-event-main-frame {
  display: flex !important;
  align-items: center !important;
}
.fc .fc-timegrid-slot-label-frame.fc-scrollgrid-shrink-frame,
.fc .fc-timegrid-axis-frame.fc-scrollgrid-shrink-frame.fc-timegrid-axis-frame-liquid,
.fc .fc-list-day-side-text {
  color: var(--color-gray-500) !important;
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
}
.fc .fc-list-day-cushion.fc-cell-shaded {
  background-color: var(--color-white) !important;
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-900) !important;
  }
}
.fc.fc-theme-standard .fc-list {
  border-color: transparent !important;
}
.fc .fc-list-day-text {
  color: var(--color-gray-900) !important;
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.fc .fc-list .fc-event {
  background-color: var(--color-gray-50) !important;
  color: var(--color-gray-900) !important;
  &:hover {
    @media (hover: hover) {
      background-color: var(--color-gray-100) !important;
    }
  }
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-800) !important;
  }
  &:where(.dark, .dark *) {
    color: var(--color-white) !important;
  }
}
.fc .fc-list .fc-event:hover {
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-700) !important;
  }
}
.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
  --tw-shadow: 0 0 #0000 !important;
  box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow) !important;
}
.fc .fc-daygrid-dot-event .fc-event-time,
.fc .fc-daygrid-dot-event .fc-event-title {
  color: var(--color-gray-500) !important;
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
}
.fc .fc-timegrid-divider {
  padding-block: calc(var(--spacing) * 0) !important;
}
@media (min-width: 768px) {
  .feed-container {
    height: calc(100vh - 4rem);
  }
}
.fc .fc-list-event:hover td {
  &:where(.dark, .dark *) {
    background-color: var(--color-gray-700) !important;
  }
}
.fc-day-today {
  background-color: var(--color-gray-50) !important;
  &:where(.dark, .dark *) {
    background-color: color-mix(in srgb, #1F2937 40%, transparent) !important;
    @supports (color: color-mix(in lab, red, red)) {
      background-color: color-mix(in oklab, var(--color-gray-800) 40%, transparent) !important;
    }
  }
}
.tiptap p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  height: calc(var(--spacing) * 0) !important;
  cursor: none !important;
  color: var(--color-gray-500) !important;
  &:where(.dark, .dark *) {
    color: var(--color-gray-400) !important;
  }
}
@layer base {
  .tooltip-arrow,.tooltip-arrow:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }
  .tooltip-arrow {
    visibility: hidden;
  }
  .tooltip-arrow:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }
  [data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  .tooltip[data-popper-placement^='top'] > .tooltip-arrow {
    bottom: -4px;
  }
  .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
    top: -4px;
  }
  .tooltip[data-popper-placement^='left'] > .tooltip-arrow {
    right: -4px;
  }
  .tooltip[data-popper-placement^='right'] > .tooltip-arrow {
    left: -4px;
  }
  .tooltip.invisible > .tooltip-arrow:before {
    visibility: hidden;
  }
  [data-popper-arrow],[data-popper-arrow]:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }
  [data-popper-arrow] {
    visibility: hidden;
  }
  [data-popper-arrow]:before {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
  }
  [data-popper-arrow]:after {
    content: "";
    visibility: visible;
    transform: rotate(45deg);
    position: absolute;
    width: 9px;
    height: 9px;
    background: inherit;
  }
  [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  .dark [role="tooltip"] > [data-popper-arrow]:before {
    border-style: solid;
    border-color: var(--color-gray-600);
  }
  [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: var(--color-gray-200);
  }
  .dark [role="tooltip"] > [data-popper-arrow]:after {
    border-style: solid;
    border-color: var(--color-gray-600);
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
    border-bottom-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-left-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
    border-top-width: 1px;
    border-right-width: 1px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
    bottom: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
    top: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
    right: -5px;
  }
  [data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
    left: -5px;
  }
  [role="tooltip"].invisible > [data-popper-arrow]:before {
    visibility: hidden;
  }
  [role="tooltip"].invisible > [data-popper-arrow]:after {
    visibility: hidden;
  }
}
@layer base {
  [type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
    appearance: none;
    background-color: #fff;
    border-color: var(--color-gray-500);
    border-width: 1px;
    border-radius: 0px;
    padding-top: 0.5rem;
    padding-right: 0.75rem;
    padding-bottom: 0.5rem;
    padding-left: 0.75rem;
    font-size: 1rem;
    line-height: 1.5rem;
    --tw-shadow: 0 0 #0000;
    &:focus {
      outline: 2px solid transparent;
      outline-offset: 2px;
      --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-color: var(--color-blue-600);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      border-color: var(--color-blue-600);
    }
  }
  input::placeholder,textarea::placeholder {
    color: var(--color-gray-500);
    opacity: 1;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  input[type="time"]::-webkit-calendar-picker-indicator {
    background: none;
  }
  select:not([size]) {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 10 6%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m1 1 4 4 4-4%27/%3e %3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 0.75em 0.75em;
    padding-right: 2.5rem;
    print-color-adjust: exact;
  }
  :is([dir=rtl]) select:not([size]) {
    background-position: left 0.75rem center;
    padding-right: 0.75rem;
    padding-left: 0;
  }
  [multiple] {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: 0.75rem;
    print-color-adjust: unset;
  }
  [type='checkbox'],[type='radio'] {
    appearance: none;
    padding: 0;
    print-color-adjust: exact;
    display: inline-block;
    vertical-align: middle;
    background-origin: border-box;
    user-select: none;
    flex-shrink: 0;
    height: 1rem;
    width: 1rem;
    color: var(--color-blue-600);
    background-color: #fff;
    border-color: --color-gray-500;
    border-width: 1px;
    --tw-shadow: 0 0 #0000;
  }
  [type='checkbox'] {
    border-radius: 0px;
  }
  [type='radio'] {
    border-radius: 100%;
  }
  [type='checkbox']:focus,[type='radio']:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
    --tw-ring-offset-width: 2px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: var(--color-blue-600);
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  [type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
    border-color: transparent !important;
    background-color: currentColor !important;
    background-size: 0.55em 0.55em;
    background-position: center;
    background-repeat: no-repeat;
  }
  [type='checkbox']:checked {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M1 5.917 5.724 10.5 15 1.5%27/%3e %3c/svg%3e");
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    print-color-adjust: exact;
  }
  [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }
  .dark [type='radio']:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
    background-size: 1em 1em;
  }
  [type='checkbox']:indeterminate {
    background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M0.5 6h14%27/%3e %3c/svg%3e");
    background-color: currentColor !important;
    border-color: transparent !important;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 0.55em 0.55em;
    print-color-adjust: exact;
  }
  [type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
    border-color: transparent !important;
    background-color: currentColor !important;
  }
  [type='file'] {
    background: unset;
    border-color: inherit;
    border-width: 0;
    border-radius: 0;
    padding: 0;
    font-size: unset;
    line-height: inherit;
  }
  [type='file']:focus {
    outline: 1px auto inherit;
  }
  input[type=file]::file-selector-button {
    color: white;
    background: var(--color-gray-800);
    border: 0;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    padding-top: 0.625rem;
    padding-bottom: 0.625rem;
    padding-left: 2rem;
    padding-right: 1rem;
    margin-inline-start: -1rem;
    margin-inline-end: 1rem;
    &:hover {
      background: var(--color-gray-700);
    }
  }
  :is([dir=rtl]) input[type=file]::file-selector-button {
    padding-right: 2rem;
    padding-left: 1rem;
  }
  .dark input[type=file]::file-selector-button {
    color: white;
    background: var(--color-gray-600);
    &:hover {
      background: var(--color-gray-500);
    }
  }
  input[type="range"]::-webkit-slider-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: var(--color-blue-600);
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }
  input[type="range"]:disabled::-webkit-slider-thumb {
    background: var(--color-gray-400);
  }
  .dark input[type="range"]:disabled::-webkit-slider-thumb {
    background: var(--color-gray-500);
  }
  input[type="range"]:focus::-webkit-slider-thumb {
    outline: 2px solid transparent;
    outline-offset: 2px;
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
    --tw-ring-opacity: 1;
    --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
  }
  input[type="range"]::-moz-range-thumb {
    height: 1.25rem;
    width: 1.25rem;
    background: var(--color-blue-600);
    border-radius: 9999px;
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
  }
  input[type="range"]:disabled::-moz-range-thumb {
    background: var(--color-gray-400);
  }
  .dark input[type="range"]:disabled::-moz-range-thumb {
    background: var(--color-gray-500);
  }
  input[type="range"]::-moz-range-progress {
    background: var(--color-blue-500);
  }
  input[type="range"]::-ms-fill-lower {
    background: var(--color-blue-500);
  }
  input[type="range"].range-sm::-webkit-slider-thumb {
    height: 1rem;
    width: 1rem;
  }
  input[type="range"].range-lg::-webkit-slider-thumb {
    height: 1.5rem;
    width: 1.5rem;
  }
  input[type="range"].range-sm::-moz-range-thumb {
    height: 1rem;
    width: 1rem;
  }
  input[type="range"].range-lg::-moz-range-thumb {
    height: 1.5rem;
    width: 1.5rem;
  }
  .toggle-bg:after {
    content: "";
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    background: white;
    border-color: var(--color-gray-300);
    border-width: 1px;
    border-radius: 9999px;
    height: 1.25rem;
    width: 1.25rem;
    transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
    transition-duration: .15s;
    box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  }
  input:checked + .toggle-bg:after {
    transform: translateX(100%);;
    border-color: white;
  }
  input:checked + .toggle-bg {
    background: var(--color-blue-600);
    border-color: var(--color-blue-600);
  }
}
@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-divide-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-divide-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-blur {
  syntax: "*";
  inherits: false;
}
@property --tw-brightness {
  syntax: "*";
  inherits: false;
}
@property --tw-contrast {
  syntax: "*";
  inherits: false;
}
@property --tw-grayscale {
  syntax: "*";
  inherits: false;
}
@property --tw-hue-rotate {
  syntax: "*";
  inherits: false;
}
@property --tw-invert {
  syntax: "*";
  inherits: false;
}
@property --tw-opacity {
  syntax: "*";
  inherits: false;
}
@property --tw-saturate {
  syntax: "*";
  inherits: false;
}
@property --tw-sepia {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false;
}
@property --tw-duration {
  syntax: "*";
  inherits: false;
}
@property --tw-ease {
  syntax: "*";
  inherits: false;
}
@property --tw-content {
  syntax: "*";
  initial-value: "";
  inherits: false;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}
@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-divide-x-reverse: 0;
      --tw-border-style: solid;
      --tw-divide-y-reverse: 0;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-content: "";
    }
  }
}

/*!*************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./node_modules/svgmap/dist/svgMap.min.css ***!
  \*************************************************************************************************************************************/
/*! svgMap | https://github.com/StephanWagner/svgMap | MIT License | Copyright Stephan Wagner | https://stephanwagner.me */
.svgMap-container,.svgMap-wrapper{position:relative}.svgMap-block-zoom-notice{position:absolute;z-index:2;top:100%;left:0;right:0;bottom:0;background:rgba(0,0,0,.8);pointer-events:none;opacity:0;color:#fff;transition:opacity 250ms}.svgMap-block-zoom-notice-active .svgMap-block-zoom-notice{pointer-events:all;top:0;opacity:1}.svgMap-block-zoom-notice>div{position:absolute;top:50%;left:0;right:0;text-align:center;padding:0 32px;transform:translateY(-50%);font-size:28px}@media (max-width:900px){.svgMap-block-zoom-notice>div{font-size:22px}}.svgMap-map-wrapper{position:relative;width:100%;padding-top:50%;overflow:hidden;background:#d9ecff;color:#111}.svgMap-map-wrapper *{box-sizing:border-box}.svgMap-map-wrapper :focus:not(:focus-visible){outline:0}.svgMap-map-wrapper .svgMap-map-image{display:block;position:absolute;top:0;left:0;width:100%;height:100%;margin:0}.svgMap-map-wrapper .svgMap-map-controls-wrapper{position:absolute;bottom:10px;left:10px;z-index:1;display:flex;overflow:hidden;border-radius:2px;box-shadow:0 0 0 2px rgba(0,0,0,.1)}.svgMap-map-wrapper .svgMap-map-controls-move,.svgMap-map-wrapper .svgMap-map-controls-zoom{display:flex;margin-right:5px;overflow:hidden;background:#fff}.svgMap-map-wrapper .svgMap-map-controls-move:last-child,.svgMap-map-wrapper .svgMap-map-controls-zoom:last-child{margin-right:0}.svgMap-map-wrapper .svgMap-control-button{background-color:transparent;border:none;border-radius:0;color:inherit;font:inherit;line-height:inherit;margin:0;padding:0;overflow:visible;text-transform:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;width:30px;height:30px;position:relative}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#666;transition:background-color 250ms}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{width:11px;height:3px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-zoom-reset-button::before{width:11px;height:11px;background:0 0;border:2px solid #666}@media (hover:hover){.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:before{background:#111}}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:before{background:#111}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:before{background:#ccc}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-zoom-reset-button.svgMap-disabled:before{border:2px solid #ccc;background:0 0}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button{margin:1px 0 1px 1px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button:after{width:3px;height:11px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-out-button{margin:1px 1px 1px 0}.svgMap-map-wrapper .svgMap-map-continent-controls-wrapper{position:absolute;top:10px;right:10px;z-index:1;display:flex;border-radius:2px;box-shadow:0 0 0 2px rgba(0,0,0,.1)}.svgMap-map-wrapper .svgMap-country{stroke:#fff;stroke-width:1;stroke-linejoin:round;vector-effect:non-scaling-stroke;transition:fill 250ms,stroke 250ms}.svgMap-map-wrapper .svgMap-country[data-link]{cursor:pointer}@media (hover:hover){.svgMap-map-wrapper .svgMap-country:hover{stroke:#333;stroke-width:1.5}}.svgMap-map-wrapper .svgMap-country.svgMap-active{stroke:#333;stroke-width:1.5}.svgMap-tooltip{box-shadow:0 0 3px rgba(0,0,0,.2);position:absolute;z-index:2;border-radius:2px;background:#fff;transform:translate(-50%,-100%);border-bottom:1px solid #000;display:none;pointer-events:none;min-width:60px}.svgMap-tooltip.svgMap-tooltip-flipped{transform:translate(-50%,0);border-bottom:0;border-top:1px solid #000}.svgMap-tooltip.svgMap-active{display:block}.svgMap-tooltip .svgMap-tooltip-content-container{position:relative;padding:10px 20px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container{text-align:center;margin:2px 0 5px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container.svgMap-tooltip-flag-container-emoji{font-size:50px;line-height:0;padding:25px 0 15px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container .svgMap-tooltip-flag{display:block;margin:auto;width:auto;height:32px;padding:2px;background:rgba(0,0,0,.15);border-radius:2px}.svgMap-tooltip .svgMap-tooltip-title{white-space:nowrap;font-size:18px;line-height:28px;padding:0 0 8px;text-align:center}.svgMap-tooltip .svgMap-tooltip-content{white-space:nowrap;text-align:center;font-size:14px;color:#777;margin:-5px 0 0}.svgMap-tooltip .svgMap-tooltip-content table{padding:0;border-spacing:0;margin:auto}.svgMap-tooltip .svgMap-tooltip-content table td{padding:2px 0;text-align:left}.svgMap-tooltip .svgMap-tooltip-content table td span{color:#111}.svgMap-tooltip .svgMap-tooltip-content table td:first-child{padding-right:10px;text-align:right}.svgMap-tooltip .svgMap-tooltip-content table td sup{vertical-align:baseline;position:relative;top:-5px}.svgMap-tooltip .svgMap-tooltip-content .svgMap-tooltip-no-data{padding:2px 0;color:#777;font-style:italic}.svgMap-tooltip .svgMap-tooltip-pointer{position:absolute;top:100%;left:50%;transform:translateX(-50%);overflow:hidden;height:10px;width:30px}.svgMap-tooltip .svgMap-tooltip-pointer:after{content:"";width:20px;height:20px;background:#fff;border:1px solid #000;position:absolute;bottom:6px;left:50%;transform:translateX(-50%) rotate(45deg)}.svgMap-tooltip.svgMap-tooltip-flipped .svgMap-tooltip-pointer{bottom:auto;top:-10px;transform:translateX(-50%) scaleY(-1)}

/*# sourceMappingURL=app.css.map*/