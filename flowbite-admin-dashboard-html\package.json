{"name": "flowbite-pro-dashboard", "version": "2.2.0", "description": "Official pro version of the admin dashboard interface from the Application UI category from Flowbite", "homepage": "https://flowbite.com/pro/", "license": "https://flowbite.com/license/", "contributors": ["<PERSON><PERSON><PERSON>gyényi <<EMAIL>>", "<PERSON> <<EMAIL>>"], "main": "static/js/app.bundle.js", "style": "dist/app.css", "scripts": {"start": "run-p start:dev:*", "start:dev:hugo": "hugo server -D --watch", "start:dev:webpack": "webpack --mode=development --watch", "build": "cross-env NODE_ENV=production && run-s build:webpack build:hugo", "build:hugo": "hugo --destination=./.build", "build:webpack": "webpack --mode=production", "build:styles": "npx tailwindcss -i ./src/app.css -o ./dist/css/app.css"}, "dependencies": {"@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@popperjs/core": "^2.10.2", "@tiptap/core": "^2.6.6", "@tiptap/extension-bold": "^2.9.1", "@tiptap/extension-color": "^2.6.6", "@tiptap/extension-font-family": "^2.6.6", "@tiptap/extension-highlight": "^2.6.6", "@tiptap/extension-horizontal-rule": "^2.6.6", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-subscript": "^2.9.1", "@tiptap/extension-superscript": "^2.9.1", "@tiptap/extension-text-align": "^2.6.6", "@tiptap/extension-text-style": "^2.6.6", "@tiptap/extension-underline": "^2.6.6", "@tiptap/extension-youtube": "^2.6.6", "@tiptap/pm": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "apexcharts": "3.46.0", "flowbite": "^3.1.2", "flowbite-datepicker": "^1.3.1", "flowbite-typography": "^1.0.4", "fullcalendar": "^6.1.11", "prismjs": "^1.25.0", "simple-datatables": "^9.2.1", "sortablejs": "^1.14.0", "svgmap": "2.10.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.0.3", "css-loader": "^6.4.0", "css-minimizer-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "mini-css-extract-plugin": "^2.4.2", "npm-run-all": "^4.1.5", "postcss": "^8.5.1", "postcss-loader": "^8.1.1", "postcss-preset-env": "^6.7.0", "source-map-loader": "^3.0.0", "style-loader": "^3.3.0", "tailwindcss": "^4.0.1", "webpack": "^5.58.2", "webpack-cli": "^4.9.0", "webpack-dev-server": "^4.3.1"}, "prosemirror-model": "1.19.3"}