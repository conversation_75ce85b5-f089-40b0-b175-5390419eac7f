<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="description" content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more." />
<meta name="author" content="Themesberg" />
<meta name="generator" content="Hugo 0.148.1" />

<title>Tailwind CSS 404 Not Found Page - Flowbite</title>

<link rel="canonical" href="http://localhost:1313/status/404/" />



<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
  rel="stylesheet"
/>


<link rel="stylesheet" href="http://localhost:1313/app.css" />

<link rel="apple-touch-icon" sizes="180x180" href="http://localhost:1313/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="http://localhost:1313/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="http://localhost:1313/favicon-16x16.png" />
<link rel="icon" type="image/png" href="http://localhost:1313/favicon.ico" />
<link rel="manifest" href="http://localhost:1313/site.webmanifest" />
<link rel="mask-icon" href="http://localhost:1313/safari-pinned-tab.svg" color="#5bbad5" />
<meta name="msapplication-TileColor" content="#ffffff" />
<meta name="theme-color" content="#ffffff" />

<!-- Twitter -->
<meta
  name="twitter:card"
  content="summary"
/>
<meta name="twitter:site" content="@" />
<meta name="twitter:creator" content="@" />
<meta name="twitter:title" content="Tailwind CSS 404 Not Found Page - Flowbite" />
<meta
  name="twitter:description"
  content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more."
/>
<meta
  name="twitter:image"
  content="http://localhost:1313/application-ui/demo/images/og-image.jpg"
/>

<!-- Facebook -->
<meta property="og:url" content="http://localhost:1313/status/404/" />
<meta property="og:title" content="Tailwind CSS 404 Not Found Page - Flowbite" />
<meta
  property="og:description"
  content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more."
/>
<meta
  property="og:type"
  content="article"
/>
<meta
  property="og:image"
  content="http://localhost:1313/application-ui/demo/images/og-image.jpg"
/>
<meta property="og:image:type" content="image/png" />



<script>
  
  if (localStorage.getItem("color-theme") === "dark" || (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
    document.documentElement.classList.add("dark");
  } else {
    document.documentElement.classList.remove("dark");
  }
</script>

  </head>
  
    <body class="bg-gray-50 dark:bg-gray-900 antialiased">
      

      
  
  <main class="bg-gray-50 dark:bg-gray-900">
    
<section class="mx-auto flex h-screen flex-col items-center justify-center px-6 dark:bg-gray-900 xl:px-0">
  <div class="block md:max-w-lg">
    <img class="mx-auto mb-4 hidden lg:flex" src="../../images/404.svg" alt="illustration" />
  </div>
  <div class="text-center xl:max-w-4xl">
    <h1 class="mb-4 text-2xl font-bold leading-tight text-primary-700 dark:text-primary-500">404 Not Found</h1>
    <p class="mb-5 text-2xl font-bold text-gray-900 dark:text-white md:text-4xl">Whoops! That page doesn’t exist.</p>
    <a
      href="http://localhost:1313/"
      class="mr-3 inline-flex items-center rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
    >
      <svg class="-ml-1 mr-2 h-5 w-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
      </svg>
      Go back home
    </a>
    <p class="my-4 text-gray-500 dark:text-gray-400 md:mt-8">Here are some helpful links instead:</p>
    <ul class="flex items-center justify-center space-x-4 text-gray-500 dark:text-gray-400">
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Home</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Search</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Help</a>
      </li>
      <li>
        <a href="#" class="underline hover:text-gray-900 dark:hover:text-white">Contact</a>
      </li>
    </ul>
  </div>
</section>

  </main>
  

      <script src="http://localhost:1313/app.bundle.js"></script>

    </body>
  
</html>
