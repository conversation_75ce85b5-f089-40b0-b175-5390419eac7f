<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect width="28" height="20" fill="#0A17A7"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.3333 9.99984C17.3333 7.99984 17.3333 6.6665 17.3333 6.6665L18.6666 7.33317L19.9999 6.6665L21.3333 7.33317L22.6666 6.6665C22.6666 6.6665 22.6666 7.99984 22.6666 9.99984C22.6666 11.9998 19.9999 13.3332 19.9999 13.3332C19.9999 13.3332 17.3333 11.9998 17.3333 9.99984Z" fill="white"/>
<mask id="mask1" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="17" y="6" width="6" height="8">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.3333 9.99984C17.3333 7.99984 17.3333 6.6665 17.3333 6.6665L18.6666 7.33317L19.9999 6.6665L21.3333 7.33317L22.6666 6.6665C22.6666 6.6665 22.6666 7.99984 22.6666 9.99984C22.6666 11.9998 19.9999 13.3332 19.9999 13.3332C19.9999 13.3332 17.3333 11.9998 17.3333 9.99984Z" fill="white"/>
</mask>
<g mask="url(#mask1)">
<rect x="17.3333" y="12" width="5.33333" height="1.33333" fill="#9ACCFF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20 9.33333C20.3682 9.33333 20.6667 9.03486 20.6667 8.66667C20.6667 8.29848 20.3682 8 20 8C19.6318 8 19.3333 8.29848 19.3333 8.66667C19.3333 9.03486 19.6318 9.33333 20 9.33333ZM18.6667 10.6667C19.0349 10.6667 19.3333 10.3682 19.3333 10C19.3333 9.63181 19.0349 9.33333 18.6667 9.33333C18.2985 9.33333 18 9.63181 18 10C18 10.3682 18.2985 10.6667 18.6667 10.6667ZM22 10C22 10.3682 21.7015 10.6667 21.3333 10.6667C20.9651 10.6667 20.6667 10.3682 20.6667 10C20.6667 9.63181 20.9651 9.33333 21.3333 9.33333C21.7015 9.33333 22 9.63181 22 10Z" fill="url(#paint0_linear)"/>
</g>
<path d="M0 -0.333333H-0.901086L-0.21693 0.253086L4.33333 4.15331V5.16179L-0.193746 8.39542L-0.333333 8.49513V8.66667V9.33333V9.93475L0.176666 9.616L5.42893 6.33333H6.55984L11.0821 9.56351C11.176 9.6306 11.2886 9.66667 11.404 9.66667C11.9182 9.66667 12.1548 9.02698 11.7644 8.69237L7.66667 5.18002V4.17154L12.0542 1.03762C12.2294 0.912475 12.3333 0.710428 12.3333 0.495127V0V-0.601416L11.8233 -0.282666L6.57107 3H5.44016L0.860413 -0.271244L0.773488 -0.333333H0.666667H0Z" fill="#FF2E3B" stroke="white" stroke-width="0.666667"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333V6H4.66667V9.33333C4.66667 9.70152 4.96514 10 5.33333 10H6.66667C7.03486 10 7.33333 9.70152 7.33333 9.33333V6H12C12.3682 6 12.6667 5.70152 12.6667 5.33333V4C12.6667 3.63181 12.3682 3.33333 12 3.33333H7.33333V0H4.66667V3.33333H0Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 4H5.33333V3.33333V0H6.66667V3.33333V4H12V5.33333H6.66667V6V9.33333H5.33333V6V5.33333H0V4Z" fill="#FF2E3B"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="18" y1="8" x2="18" y2="10.6667" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFA51B"/>
<stop offset="1" stop-color="#FF9A00"/>
</linearGradient>
</defs>
</svg>
