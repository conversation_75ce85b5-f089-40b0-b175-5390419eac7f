<!-- Content here -->
<aside id="sidebar-music-playlists" class="fixed right-0 h-svh w-80 translate-x-full bg-white py-20 transition-transform dark:bg-gray-800 lg:pt-0 xl:!translate-x-0" aria-label="Sidebar">
  <div class="h-full overflow-y-auto border-l border-gray-200 bg-white px-3 py-4 dark:border-gray-700 dark:bg-gray-800">
    <div class="mb-4 flex items-center justify-between text-sm font-medium text-gray-500 dark:text-gray-400">
      <h3 class="uppercase">Trending songs</h3>
      <a href="#" class="text-sm font-medium text-primary-700 underline hover:no-underline dark:text-primary-500">Show all</a>
    </div>
    <ul class="space-y-4 border-b border-gray-200 pb-5 dark:border-gray-700">
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-2.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Blinding Lights</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">The Weeknd</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-3.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">My Love</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Florence + The Machine</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-4.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Crash</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Jose & Bbb Band</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-5.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Maybe You</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Bonnie Green</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-6.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">I'm Yours</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Jese Leos feat. Tymo</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-7.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">My Universe</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Leslie Livingston</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-8.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Fils de Joie</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Rob Casas</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-9.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">That’s What I Want</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Mc Fallen & Toybr</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-10.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Shivers</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Keys Neves</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-11.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Woman</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Karen Nelson</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
    </ul>
    <div class="mb-4 flex items-center justify-between pt-4 text-sm font-medium text-gray-500 dark:text-gray-400">
      <h3 class="uppercase">Trending albums</h3>
      <a href="#" class="text-sm font-medium text-primary-700 underline hover:no-underline dark:text-primary-500">Show all</a>
    </div>
    <ul class="space-y-4">
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-2.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Blinding Lights</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">The Weeknd</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-3.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">My Love</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Florence + The Machine</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-4.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Crash</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Jose & Bbb Band</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-5.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Maybe You</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Bonnie Green</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-6.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">I'm Yours</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Jese Leos feat. Tymo</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-7.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">My Universe</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Leslie Livingston</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-8.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Fils de Joie</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Rob Casas</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-9.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">That’s What I Want</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Mc Fallen & Toybr</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-10.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Shivers</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Keys Neves</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
      <li>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <a href="#">
              <img class="h-9 w-9 rounded-md" src="../../images/music/image-11.jpg" alt="music image" />
            </a>
            <div>
              <a href="#">
                <h4 class="mb-0.5 font-semibold leading-tight text-gray-900 dark:text-white hover:underline">Woman</h4>
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Karen Nelson</p>
            </div>
          </div>
          <button
            type="button"
            class="inline-flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
          >
            <svg class="h-5 w-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd" />
            </svg>

            <span class="sr-only">Play button</span>
          </button>
        </div>
      </li>
    </ul>
  </div>
</aside>
