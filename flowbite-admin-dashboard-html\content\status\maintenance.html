---
title: Tailwind CSS Maintenance Page - Flowbite
description: Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more.
layout: main
group: status
page: maintenance
---

<section class="mx-auto flex h-screen flex-col items-center justify-center bg-white dark:bg-gray-900">
  <div class="max-w-screen-xl grid-cols-12 content-center gap-8 px-4 py-8 sm:grid lg:px-6 lg:py-16">
    <div class="self-center sm:col-span-7 lg:col-span-6">
      <a href="/" class="inline-flex items-center text-xl font-semibold text-gray-900 dark:text-white">
        <img class="mr-2 h-8 w-8" src="/images/logo.svg" alt="logo" />
        Flowbite
      </a>
      <h1 class="my-4 text-3xl font-bold text-gray-900 dark:text-white md:my-6 md:text-4xl">Down for Maintenance</h1>
      <p class="mb-2 text-gray-500 dark:text-gray-400">Flowbite is down for planned maintenance. We'll be back with the latest components and sections soon.</p>
      <p class="mb-4 text-gray-500 dark:text-gray-400 md:mb-6">
        Follow us on <a href="#" class="font-medium text-primary-700 hover:underline dark:text-primary-500">twitter</a> or our <a href="#" class="font-medium text-primary-700 hover:underline dark:text-primary-500">blog</a> for latest
        updates.
      </p>
      <button
        type="submit"
        class="rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
      >
        Contact us
      </button>
    </div>
    <div class="hidden sm:col-span-5 sm:flex lg:col-span-6">
      <img class="mx-auto mb-4 h-auto w-44 dark:hidden lg:w-auto" src="../../images/maintenance.svg" alt="illustration" />
      <img class="mx-auto mb-4 hidden h-auto w-44 dark:flex lg:w-auto" src="../../images/maintenance-dark.svg" alt="illustration" />
    </div>
  </div>
</section>
