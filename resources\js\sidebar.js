// Sidebar functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing sidebar...');

    // Wait for Flowbite to be loaded and initialized
    setTimeout(() => {
        const sidebar = document.getElementById('sidebar');
        const toggleButton = document.getElementById('toggleSidebarButton');
        const mainContent = document.getElementById('main-content');

        console.log('Sidebar element:', sidebar);
        console.log('Toggle button:', toggleButton);
        console.log('Main content:', mainContent);

        if (sidebar && toggleButton && mainContent) {
            console.log('All sidebar elements found, setting up...');

            // Force show sidebar on desktop
            function ensureDesktopSidebar() {
                if (window.innerWidth >= 1024) { // lg breakpoint
                    console.log('Desktop mode - showing sidebar');
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    sidebar.style.display = 'block';
                    sidebar.style.visibility = 'visible';

                    // Ensure main content has proper margin
                    mainContent.classList.add('lg:ms-64');
                    mainContent.classList.remove('lg:ms-0');
                } else {
                    console.log('Mobile mode - hiding sidebar');
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');

                    // Remove margin on mobile
                    mainContent.classList.remove('lg:ms-64', 'lg:ms-0');
                }
            }

            // Initial setup
            ensureDesktopSidebar();

            // Handle window resize
            window.addEventListener('resize', ensureDesktopSidebar);

            // Toggle functionality
            if (toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    console.log('Toggle button clicked');
                    e.preventDefault();

                    if (window.innerWidth >= 1024) {
                        // Desktop toggle
                        sidebar.classList.toggle('-translate-x-full');
                        sidebar.classList.toggle('translate-x-0');

                        if (sidebar.classList.contains('-translate-x-full')) {
                            // Sidebar is hidden
                            mainContent.classList.remove('lg:ms-64');
                            mainContent.classList.add('lg:ms-0');
                            console.log('Sidebar hidden on desktop');
                        } else {
                            // Sidebar is visible
                            mainContent.classList.remove('lg:ms-0');
                            mainContent.classList.add('lg:ms-64');
                            console.log('Sidebar shown on desktop');
                        }
                    }
                });
            }
        } else {
            console.log('Some sidebar elements not found');
            if (!sidebar) console.log('Sidebar element not found');
            if (!toggleButton) console.log('Toggle button not found');
            if (!mainContent) console.log('Main content not found');
        }
    }, 100); // Small delay to ensure DOM is fully ready
});
