// Sidebar functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing sidebar...');
    
    // Wait for Flowbite to be loaded and initialized
    setTimeout(() => {
        const sidebar = document.getElementById('sidebar');
        const toggleButton = document.getElementById('toggleSidebarButton');
        
        console.log('Sidebar element:', sidebar);
        console.log('Toggle button:', toggleButton);
        
        if (sidebar && toggleButton) {
            console.log('Sidebar elements found, setting up...');
            
            // Force show sidebar on desktop
            function ensureDesktopSidebar() {
                if (window.innerWidth >= 1024) { // lg breakpoint
                    console.log('Desktop mode - showing sidebar');
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    sidebar.style.display = 'block';
                    sidebar.style.visibility = 'visible';
                } else {
                    console.log('Mobile mode - hiding sidebar');
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                }
            }
            
            // Initial setup
            ensureDesktopSidebar();
            
            // Handle window resize
            window.addEventListener('resize', ensureDesktopSidebar);
            
            // Manual toggle for debugging
            if (toggleButton) {
                toggleButton.addEventListener('click', function(e) {
                    console.log('Toggle button clicked');
                    e.preventDefault();
                    
                    if (window.innerWidth >= 1024) {
                        // Desktop toggle
                        if (sidebar.classList.contains('-translate-x-full')) {
                            sidebar.classList.remove('-translate-x-full');
                            sidebar.classList.add('translate-x-0');
                        } else {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');
                        }
                    }
                });
            }
        } else {
            console.error('Sidebar elements not found!');
            console.log('Available elements with ID:', Array.from(document.querySelectorAll('[id]')).map(el => el.id));
        }
    }, 200);
});
