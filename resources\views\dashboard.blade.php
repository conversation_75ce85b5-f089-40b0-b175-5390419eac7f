@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
<div class="px-4 pt-4">
    <!-- <PERSON> Header -->
    <div class="mb-4">
        <nav class="flex mb-5" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 text-sm font-medium md:space-x-2">
                <li class="inline-flex items-center">
                    <a href="#" class="inline-flex items-center text-gray-700 hover:text-primary-600 dark:text-gray-300 dark:hover:text-white">
                        <svg class="w-5 h-5 mr-2.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-400 md:ml-2 dark:text-gray-500" aria-current="page">Dashboard</span>
                    </div>
                </li>
            </ol>
        </nav>
        <h1 class="text-xl font-semibold text-gray-900 sm:text-2xl dark:text-white">Dashboard</h1>
        <p class="text-sm text-gray-600 dark:text-gray-400">Welcome back, {{ auth()->user()->name }}!</p>
    </div>

    @if(auth()->user()->hasPermission('dashboard.analytics'))
    <!-- Stats Cards -->
    <div class="mt-4 grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-4">
    <!-- Total Users Card -->
    <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="w-full">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Total Users</h3>
            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['total_users'] ?? 0 }}</span>
            <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"></path>
                    </svg>
                    12.5%
                </span>
                Since last month
            </p>
        </div>
    </div>

    <!-- Active Users Card -->
    <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="w-full">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">Active Users</h3>
            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['active_users'] ?? 0 }}</span>
            <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                <span class="flex items-center mr-1.5 text-sm text-green-500 dark:text-green-400">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path clip-rule="evenodd" fill-rule="evenodd" d="M10 17a.75.75 0 01-.75-.75V5.612L5.29 9.77a.75.75 0 01-1.08-1.04l5.25-5.5a.75.75 0 011.08 0l5.25 5.5a.75.75 0 11-1.08 1.04L10.75 5.612V16.25A.75.75 0 0110 17z"></path>
                    </svg>
                    8.5%
                </span>
                Since last week
            </p>
        </div>
    </div>

    <!-- New Users Today Card -->
    <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="w-full">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">New Today</h3>
            <span class="text-2xl font-bold leading-none text-gray-900 sm:text-3xl dark:text-white">{{ $stats['new_users_today'] ?? 0 }}</span>
            <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                <span class="flex items-center mr-1.5 text-sm text-red-500 dark:text-red-400">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <path clip-rule="evenodd" fill-rule="evenodd" d="M10 3a.75.75 0 01.75.75v10.638l3.96-4.158a.75.75 0 111.08 1.04l-5.25 5.5a.75.75 0 01-1.08 0l-5.25-5.5a.75.75 0 111.08-1.04l3.96 4.158V3.75A.75.75 0 0110 3z"></path>
                    </svg>
                    1.4%
                </span>
                Since yesterday
            </p>
        </div>
    </div>

    <!-- System Status Card -->
    <div class="items-center justify-between p-4 bg-white border border-gray-200 rounded-lg shadow-sm sm:flex dark:border-gray-700 sm:p-6 dark:bg-gray-800">
        <div class="w-full">
            <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">System Status</h3>
            <span class="text-2xl font-bold leading-none text-green-500 sm:text-3xl dark:text-green-400">Online</span>
            <p class="flex items-center text-base font-normal text-gray-500 dark:text-gray-400">
                <span class="inline-flex items-center bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full dark:bg-green-900 dark:text-green-300">
                    <span class="w-2 h-2 mr-1 bg-green-500 rounded-full"></span>
                    Operational
                </span>
                All systems running
            </p>
        </div>
    </div>
    </div>
    
    <!-- Charts and Activity Section -->
    <div class="mt-4 grid w-full grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
        <!-- Main Chart -->
        <div class="bg-white shadow rounded-lg p-4 sm:p-6 xl:p-8 dark:bg-gray-800">
        <div class="flex items-center justify-between mb-4">
            <div class="flex-shrink-0">
                <span class="text-2xl sm:text-3xl leading-none font-bold text-gray-900 dark:text-white">$45,385</span>
                <h3 class="text-base font-normal text-gray-500 dark:text-gray-400">New users this week</h3>
            </div>
            <div class="flex items-center justify-end flex-1 text-base font-medium text-green-500 dark:text-green-400">
                12.5%
                <svg class="w-5 h-5 ml-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                </svg>
            </div>
        </div>
        <!-- Chart placeholder -->
        <div class="h-72 bg-gray-50 rounded-lg dark:bg-gray-700 flex items-center justify-center">
            <div class="text-center">
                <svg class="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p class="text-gray-500 dark:text-gray-400">User Growth Chart</p>
                <p class="text-sm text-gray-400 dark:text-gray-500">Chart will be implemented here</p>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow rounded-lg dark:bg-gray-800">
        <div class="flex items-center justify-between p-4 sm:p-6 xl:p-8">
            <div>
                <h3 class="text-xl leading-none font-bold text-gray-900 mb-2 dark:text-white">Recent Activity</h3>
                <span class="text-base font-normal text-gray-500 dark:text-gray-400">This is a list of latest activity</span>
            </div>
        </div>
        <div class="p-4 sm:p-6 xl:p-8 pt-0">
            @if(isset($stats['recent_activities']) && $stats['recent_activities']->count() > 0)
            <div class="flow-root">
                <ul role="list" class="divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($stats['recent_activities']->take(5) as $activity)
                    <li class="py-3 sm:py-4">
                        <div class="flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center dark:bg-primary-900">
                                    <svg class="w-4 h-4 text-primary-600 dark:text-primary-300" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate dark:text-white">
                                    {{ $activity->description }}
                                </p>
                                @if($activity->causer)
                                <p class="text-sm text-gray-500 truncate dark:text-gray-400">
                                    by {{ $activity->causer->name ?? $activity->causer->email }}
                                </p>
                                @endif
                            </div>
                            <div class="inline-flex items-center text-xs font-normal text-gray-500 dark:text-gray-400">
                                {{ $activity->created_at->diffForHumans() }}
                            </div>
                        </div>
                    </li>
                    @endforeach
                </ul>
            </div>
            @else
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No recent activity</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Activity will appear here as users interact with the system.</p>
            </div>
            @endif
        </div>
    </div>
</div>    <!-- Additional Stats Row -->
    <div class="grid grid-cols-1 xl:grid-cols-3 gap-4 mb-4">
    <!-- Quick Actions Card -->
    <div class="bg-white shadow rounded-lg dark:bg-gray-800">
        <div class="p-4 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h3>
            <div class="space-y-3">
                @can('users.create')
                <a href="{{ route('admin.users.create') }}" class="flex items-center p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">
                    <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path>
                    </svg>
                    <span class="ml-3">Add New User</span>
                </a>
                @endcan
                @can('roles.view')
                <a href="{{ route('admin.roles.index') }}" class="flex items-center p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">
                    <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-3">Manage Roles</span>
                </a>
                @endcan
                <a href="{{ route('profile.show') }}" class="flex items-center p-3 text-base font-bold text-gray-900 rounded-lg bg-gray-50 hover:bg-gray-100 group hover:shadow dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-white">
                    <svg class="w-5 h-5 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-3">View Profile</span>
                </a>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="bg-white shadow rounded-lg dark:bg-gray-800">
        <div class="p-4 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">System Information</h3>
            <div class="space-y-4">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Laravel Version</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ app()->version() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">PHP Version</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ phpversion() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Environment</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ app()->environment() }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">Database</span>
                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ config('database.default') }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- User Roles Distribution -->
    <div class="bg-white shadow rounded-lg dark:bg-gray-800">
        <div class="p-4 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">User Roles</h3>
            <div class="space-y-3">
                @if(isset($stats['role_distribution']))
                    @foreach($stats['role_distribution'] as $role => $count)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-primary-600 mr-2"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">{{ ucfirst($role) }}</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900 dark:text-white">{{ $count }}</span>
                    </div>
                    @endforeach
                @else
                <div class="text-center py-4">
                    <p class="text-sm text-gray-500 dark:text-gray-400">Role statistics will appear here</p>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>    @else
    <!-- Basic Welcome for Users without Analytics Permission -->
    <div class="grid grid-cols-1 gap-4 mb-4">
        <div class="bg-white shadow rounded-lg dark:bg-gray-800">
        <div class="p-4 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">Welcome to {{ config('app.name') }}</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                You're successfully logged in! Use the navigation menu to access your available features.
            </p>
            
            <!-- Quick Actions -->
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <a href="{{ route('profile.show') }}" class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500 dark:bg-gray-700 dark:border-gray-600">
                    <div class="flex-shrink-0">
                        <svg class="h-10 w-10 text-primary-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <span class="absolute inset-0" aria-hidden="true"></span>
                        <p class="text-sm font-medium text-gray-900 dark:text-white">View Profile</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Manage your account settings</p>
                    </div>
                </a>

                <div class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 dark:bg-gray-700 dark:border-gray-600">
                    <div class="flex-shrink-0">
                        <svg class="h-10 w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">Account Status</p>
                        <p class="text-sm text-green-600 dark:text-green-400">Active & Verified</p>
                    </div>
                </div>
            </div>
        </div>        </div>
    </div>
    @endif
</div>
@endsection
