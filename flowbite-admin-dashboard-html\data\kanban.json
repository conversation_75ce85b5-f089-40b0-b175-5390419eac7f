[{"id": 1, "title": "To Do", "tasks": [{"id": 32, "name": "Change charts javascript", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 5, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 23, "name": "Change homepage", "description": "Change homepage for Volt Dashboard.", "completed": false, "daysLeft": 22, "attachment": "/images/kanban/task-4.png", "attachmentDark": "/images/kanban/task-4-dark.png", "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 65, "name": "Change charts javascript", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 7, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}, {"id": 2, "title": "In Progress", "tasks": [{"id": 76, "name": "Redesign tables card", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 9, "attachment": "/images/kanban/task-1.jpg", "attachmentDark": "/images/kanban/task-1-dark.jpg", "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 49, "name": "Redesign tables card", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": false, "daysLeft": 3, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}, {"id": 3, "title": "Done", "tasks": [{"id": 87, "name": "Redesign tables card", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": true, "daysLeft": 0, "attachment": "/images/kanban/task-2.jpg", "attachmentDark": "/images/kanban/task-2-dark.jpg", "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 43, "name": "Redesign tables card", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": true, "daysLeft": 0, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}, {"id": 34, "name": "Create Javascript elements", "description": "In _variables.scss on line 672 you define $table_variants. Each instance of \"color-level\" needs to be changed to \"shift-color\".", "completed": true, "daysLeft": 0, "members": [{"id": 1, "name": "<PERSON>", "avatar": "bonnie-green.png"}, {"id": 2, "name": "<PERSON>", "avatar": "roberta-casas.png"}, {"id": 3, "name": "<PERSON>", "avatar": "michael-gough.png"}]}]}]