<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="#F5F5F5" stroke-width="0.5"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect x="0.25" y="0.25" width="27.5" height="19.5" rx="1.75" fill="white" stroke="white" stroke-width="0.5"/>
</mask>
<g mask="url(#mask0)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 13.3334H28V6.66675H9.24398V5.33342C9.24398 5.33342 8.30367 4.66675 7.33333 4.66675C6.36299 4.66675 5.33333 5.33342 5.33333 5.33342V6.66675H0V13.3334ZM8.66667 8.00008C8.66667 8.73646 8.06971 9.33342 7.33333 9.33342C6.59695 9.33342 6 8.73646 6 8.00008C6 7.2637 6.59695 6.66675 7.33333 6.66675C8.06971 6.66675 8.66667 7.2637 8.66667 8.00008Z" fill="#0C47B7"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 19.9999H28V13.3333H0V19.9999Z" fill="#E53B35"/>
</g>
</g>
<defs>
<filter id="filter0_d" x="0" y="13.3333" width="28" height="6.66667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
