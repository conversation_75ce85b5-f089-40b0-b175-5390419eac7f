<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="description" content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more." />
<meta name="author" content="Themesberg" />
<meta name="generator" content="Hugo 0.148.1" />

<title>Tailwind CSS Forgot Password Page - Flowbite</title>

<link rel="canonical" href="http://localhost:1313/authentication/forgot-password/" />



<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
  href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap"
  rel="stylesheet"
/>


<link rel="stylesheet" href="http://localhost:1313/app.css" />

<link rel="apple-touch-icon" sizes="180x180" href="http://localhost:1313/apple-touch-icon.png" />
<link rel="icon" type="image/png" sizes="32x32" href="http://localhost:1313/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="http://localhost:1313/favicon-16x16.png" />
<link rel="icon" type="image/png" href="http://localhost:1313/favicon.ico" />
<link rel="manifest" href="http://localhost:1313/site.webmanifest" />
<link rel="mask-icon" href="http://localhost:1313/safari-pinned-tab.svg" color="#5bbad5" />
<meta name="msapplication-TileColor" content="#ffffff" />
<meta name="theme-color" content="#ffffff" />

<!-- Twitter -->
<meta
  name="twitter:card"
  content="summary"
/>
<meta name="twitter:site" content="@" />
<meta name="twitter:creator" content="@" />
<meta name="twitter:title" content="Tailwind CSS Forgot Password Page - Flowbite" />
<meta
  name="twitter:description"
  content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more."
/>
<meta
  name="twitter:image"
  content="http://localhost:1313/application-ui/demo/images/og-image.jpg"
/>

<!-- Facebook -->
<meta property="og:url" content="http://localhost:1313/authentication/forgot-password/" />
<meta property="og:title" content="Tailwind CSS Forgot Password Page - Flowbite" />
<meta
  property="og:description"
  content="Get started with a premium admin dashboard interface built with Tailwind CSS and Flowbite featuring over 50 example pages of charts, calendars, kanban boards, dashboards, CRUD pages, mailing systems, and more."
/>
<meta
  property="og:type"
  content="article"
/>
<meta
  property="og:image"
  content="http://localhost:1313/application-ui/demo/images/og-image.jpg"
/>
<meta property="og:image:type" content="image/png" />



<script>
  
  if (localStorage.getItem("color-theme") === "dark" || (!("color-theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
    document.documentElement.classList.add("dark");
  } else {
    document.documentElement.classList.remove("dark");
  }
</script>

  </head>
  
    <body class="bg-gray-50 dark:bg-gray-900 antialiased">
      

      
  
  <main class="bg-gray-50 dark:bg-gray-900">
    
<section class="bg-gray-50 dark:bg-gray-900">
  <div class="mx-auto grid h-screen max-w-screen-xl justify-items-center px-4 py-8 lg:grid-cols-12 lg:gap-20 lg:py-16">
    <div class="place-self-center lg:col-span-6">
      <div class="max-w-lg rounded-lg bg-white p-4 shadow-sm dark:bg-gray-800 sm:p-6">
        <a href="#" class="mb-4 inline-flex items-center text-xl font-semibold text-gray-900 dark:text-white sm:mb-6">
          <img class="mr-2 h-8 w-8" src="http://localhost:1313/images/logo.svg" alt="logo" />
          Flowbite
        </a>
        <h1 class="mb-2 text-2xl font-extrabold leading-tight tracking-tight text-gray-900 dark:text-white">Reset your password</h1>
        <p class=" text-gray-500 dark:text-gray-400">
          We’ll email you instructions to reset your password. If you don’t have access to your email anymore, you can try
          <a class="font-medium text-primary-700 underline hover:no-underline dark:text-primary-500" href="#">account recovery</a>.
        </p>
        <form action="#">
          <div class="my-4 sm:my-6">
            <label for="email" class="mb-2 block text-sm font-medium text-gray-900 dark:text-white">Email address</label>
            <input
              type="email"
              id="email"
              class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder:text-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500"
              placeholder="<EMAIL>"
              required
            />
          </div>
          <div class="my-4 flex items-start sm:my-6">
            <div class="flex h-5 items-center">
              <input
                id="terms"
                aria-describedby="terms"
                type="checkbox"
                class="h-4 w-4 rounded-sm border border-gray-300 bg-gray-50 focus:ring-2 focus:ring-primary-300 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800 dark:focus:ring-primary-600"
                required=""
              />
            </div>
            <div class="ml-3 text-sm">
              <label for="terms" class="text-gray-500 dark:text-gray-300"
                >I agree to Flowbite’s <a class="font-medium text-primary-700 hover:underline dark:text-primary-500" href="#">Terms of Use</a> and
                <a class="font-medium text-primary-700 hover:underline dark:text-primary-500" href="#">Privacy Policy</a>.</label
              >
            </div>
          </div>
          <button
            type="submit"
            class="w-full rounded-lg bg-primary-700 px-5 py-2.5 text-center text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800"
          >
            Send confirmation email
          </button>
          <p class="mt-4 text-sm text-gray-500 dark:text-gray-400 sm:mt-6 sm:text-center">
            If you still need help, contact <a class="font-medium text-primary-700 underline hover:no-underline dark:text-primary-500" href="#">Flowbite Support.</a>
          </p>
        </form>
      </div>
    </div>
    <div class="mr-auto hidden place-self-center lg:col-span-6 lg:flex">
      <img class="mx-auto dark:hidden" src="../../images/girl-and-computer.svg" alt="illustration" />
      <img class="mx-auto hidden dark:flex" src="../../images/girl-and-computer-dark.svg" alt="illustration" />
    </div>
  </div>
</section>

  </main>
  

      <script src="http://localhost:1313/app.bundle.js"></script>

    </body>
  
</html>
