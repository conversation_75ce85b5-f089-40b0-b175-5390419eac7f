{{ define "main" }}
  <header class="border-bottom py-5">
    <div class="pt-md-1 pb-md-4 container">
      <h1 class="bd-title mt-0">{{ .Title | markdownify }}</h1>
      <p class="bd-lead">{{ .Page.Params.Description | markdownify }}</p>
      {{ if eq .Title "Examples" }}
        <div class="d-flex flex-column flex-sm-row">
          <a href="{{ .Site.Params.download.dist_examples }}" class="btn btn-lg btn-bd-primary" onclick="ga('send', 'event', 'Examples', 'Hero', 'Download Examples');">Download examples</a>
        </div>
      {{ end }}
    </div>
  </header>

  <main class="bd-content order-1 py-5" id="content">
    <div class="container">
      {{ .Content }}
    </div>
  </main>
{{ end }}
