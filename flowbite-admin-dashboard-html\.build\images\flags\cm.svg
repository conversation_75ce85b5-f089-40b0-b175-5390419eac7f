<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect x="13.3333" width="14.6667" height="20" fill="url(#paint0_linear)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 20H9.33333V0H0V20Z" fill="#059170"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.33325 20H18.6666V0H9.33325V20Z" fill="#E21A30"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.6667 20H28.0001V0H18.6667V20Z" fill="#FFDC44"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.0001 11.2998L12.0408 12.6966L12.7637 10.4016L10.8299 8.96978L13.236 8.94812L14.0001 6.6665L14.7642 8.94812L17.1703 8.96978L15.2365 10.4016L15.9594 12.6966L14.0001 11.2998Z" fill="#FFDC42"/>
</g>
<defs>
<linearGradient id="paint0_linear" x1="13.3333" y1="0" x2="13.3333" y2="20" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFDC44"/>
<stop offset="1" stop-color="#FDD216"/>
</linearGradient>
</defs>
</svg>
