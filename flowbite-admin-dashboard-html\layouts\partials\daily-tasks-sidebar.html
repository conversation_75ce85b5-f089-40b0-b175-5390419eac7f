<!-- Content here -->
<aside class="fixed right-0 h-full w-80 translate-x-full border-l border-gray-200 bg-white pt-20 transition-transform dark:border-gray-700 dark:bg-gray-800 lg:pt-0 xl:!translate-x-0" aria-label="Profilebar" id="user-drawer">
  <div class="h-full overflow-y-auto bg-white px-3 py-5 dark:bg-gray-800">
    <div class="flex items-center justify-between">
      <button
        type="button"
        class="rounded-lg border border-gray-200 bg-white p-1.5 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
      >
        <svg class="h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m15 19-7-7 7-7" />
        </svg>
      </button>
      <h3 class="font-medium text-gray-900 dark:text-white">Tue, Sep 18</h3>
      <button
        type="button"
        class="rounded-lg border border-gray-200 bg-white p-1.5 text-sm font-medium text-gray-900 hover:bg-gray-100 hover:text-primary-700 focus:z-10 focus:outline-none focus:ring-4 focus:ring-gray-100 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700"
      >
        <svg class="h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7" />
        </svg>
      </button>
    </div>
    <ol class="space-y-6 py-6">
      <li class="flex items-center">
        <time datetime="06:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">06:00 am</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="07:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">07:00 am</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li>
        <div class="mb-6 flex items-center">
          <time datetime="08:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">08:00 am</time>
          <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <a href="#" class="flex items-start rounded-lg bg-purple-600 p-2 text-white hover:bg-purple-500">
          <svg class="me-1.5 h-4 w-4 text-white shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M5 9a7 7 0 1 1 8 7v5a1 1 0 1 1-2 0v-5a7 7 0 0 1-6-7Zm6-1c.2-.3.6-.5 1-.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.4.2-.8.4-1Z" clip-rule="evenodd" />
          </svg>
          <div class="leading-none space-y-1.5">
            <time datetime="08:15" class="text-xs font-normal leading-none block">08:15 am</time>
            <p class="text-sm font-medium">Video presentation for Flowbite Inc</p>
          </div>
        </a>
      </li>
      <li class="flex items-center">
        <time datetime="06:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">09:00 am</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li>
        <div class="mb-6 flex items-center">
          <time datetime="10:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">10:00 am</time>
          <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <a href="#" class="flex items-start rounded-lg bg-primary-600 p-2 text-white hover:bg-primary-500">
          <svg class="me-1.5 h-4 w-4 text-white shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M5 9a7 7 0 1 1 8 7v5a1 1 0 1 1-2 0v-5a7 7 0 0 1-6-7Zm6-1c.2-.3.6-.5 1-.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.4.2-.8.4-1Z" clip-rule="evenodd" />
          </svg>
          <div class="leading-none space-y-1.5">
            <time datetime="10:25" class="text-xs font-normal leading-none block">10:25 am - 11:00 am</time>
            <p class="text-sm font-medium">Meeting with Bonnie Green and Jese Leos</p>
          </div>
        </a>
      </li>
      <li class="flex items-center">
        <time datetime="11:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">11:00 am</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="12:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">12:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="13:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">13:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li>
        <div class="mb-6 flex items-center">
          <time datetime="14:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">14:00 pm</time>
          <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <a href="#" class="flex items-start rounded-lg bg-teal-600 p-2 text-white hover:bg-teal-500">
          <svg class="me-1.5 h-4 w-4 text-white shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M5 9a7 7 0 1 1 8 7v5a1 1 0 1 1-2 0v-5a7 7 0 0 1-6-7Zm6-1c.2-.3.6-.5 1-.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.4.2-.8.4-1Z" clip-rule="evenodd" />
          </svg>
          <div class="leading-none space-y-1.5">
            <time datetime="14:00" class="text-xs font-normal leading-none block">14:00 pm - 14:30 pm</time>
            <p class="text-sm font-medium">Planning for the new website</p>
          </div>
        </a>
      </li>
      <li class="flex items-center">
        <time datetime="15:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">15:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="16:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">16:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li>
        <div class="mb-6 flex items-center">
          <time datetime="17:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">17:00 pm</time>
          <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <a href="#" class="flex items-start rounded-lg bg-pink-600 p-2 text-white hover:bg-pink-500">
          <svg class="me-1.5 h-4 w-4 text-white shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M5 9a7 7 0 1 1 8 7v5a1 1 0 1 1-2 0v-5a7 7 0 0 1-6-7Zm6-1c.2-.3.6-.5 1-.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.4.2-.8.4-1Z" clip-rule="evenodd" />
          </svg>
          <div class="leading-none space-y-1.5">
            <time datetime="17:00" class="text-xs font-normal leading-none block">17:15 pm - 18:00 pm</time>
            <p class="text-sm font-medium">Presentation of Flowbite</p>
          </div>
        </a>
      </li>
      <li class="flex items-center">
        <time datetime="18:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">18:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="19:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">19:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li>
        <div class="mb-6 flex items-center">
          <time datetime="17:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">20:00 pm</time>
          <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
        </div>
        <a href="#" class="flex items-start rounded-lg bg-indigo-600 p-2 text-white hover:bg-indigo-500">
          <svg class="me-1.5 h-4 w-4 text-white shrink-0" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
            <path fill-rule="evenodd" d="M5 9a7 7 0 1 1 8 7v5a1 1 0 1 1-2 0v-5a7 7 0 0 1-6-7Zm6-1c.2-.3.6-.5 1-.5a1 1 0 1 0 0-2A3.5 3.5 0 0 0 8.5 9a1 1 0 0 0 2 0c0-.4.2-.8.4-1Z" clip-rule="evenodd" />
          </svg>
          <div class="leading-none space-y-1.5">
            <time datetime="20:00" class="text-xs font-normal leading-none block">20:00 pm - 21:00 pm</time>
            <p class="text-sm font-medium">Meeting with Flowbite CEO to discuss financial results</p>
          </div>
        </a>
      </li>
      <li class="flex items-center">
        <time datetime="21:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">21:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="22:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">22:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="23:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">23:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
      <li class="flex items-center">
        <time datetime="24:00" class="w-20 text-xs font-normal text-gray-500 dark:text-gray-400">24:00 pm</time>
        <div class="h-px w-full bg-gray-200 dark:bg-gray-700"></div>
      </li>
    </ol>
    <button
      id="newTaskButton"
      data-modal-target="newTaskModal"
      data-modal-toggle="newTaskModal"
      type="button"
      class="flex w-full items-center justify-center rounded-lg bg-primary-700 px-3 py-2 text-sm font-medium text-white hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-800 sm:w-auto"
    >
      <svg class="-ms-0.5 me-1.5 h-4 w-4" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5" />
      </svg>
      Add new task
    </button>
  </div>
</aside>
