/*!***********************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./src/style.css ***!
  \***********************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(63 131 248 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #E5E7EB; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9CA3AF; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9CA3AF; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.tooltip-arrow,.tooltip-arrow:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

.tooltip-arrow {
  visibility: hidden;
}

.tooltip-arrow:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-tooltip-style^='light'] + .tooltip > .tooltip-arrow:before {
  border-style: solid;
  border-color: #e5e7eb;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='top'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='right'] > .tooltip-arrow:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='bottom'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-tooltip-style^='light'] + .tooltip[data-popper-placement^='left'] > .tooltip-arrow:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

.tooltip[data-popper-placement^='top'] > .tooltip-arrow {
  bottom: -4px;
}

.tooltip[data-popper-placement^='bottom'] > .tooltip-arrow {
  top: -4px;
}

.tooltip[data-popper-placement^='left'] > .tooltip-arrow {
  right: -4px;
}

.tooltip[data-popper-placement^='right'] > .tooltip-arrow {
  left: -4px;
}

.tooltip.invisible > .tooltip-arrow:before {
  visibility: hidden;
}

[data-popper-arrow],[data-popper-arrow]:before {
  position: absolute;
  width: 8px;
  height: 8px;
  background: inherit;
}

[data-popper-arrow] {
  visibility: hidden;
}

[data-popper-arrow]:before {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
}

[data-popper-arrow]:after {
  content: "";
  visibility: visible;
  transform: rotate(45deg);
  position: absolute;
  width: 9px;
  height: 9px;
  background: inherit;
}

[role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:before {
  border-style: solid;
  border-color: #4b5563;
}

[role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #e5e7eb;
}

.dark [role="tooltip"] > [data-popper-arrow]:after {
  border-style: solid;
  border-color: #4b5563;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:before {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow]:after {
  border-bottom-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-left-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:before {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow]:after {
  border-top-width: 1px;
  border-right-width: 1px;
}

[data-popover][role="tooltip"][data-popper-placement^='top'] > [data-popper-arrow] {
  bottom: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='bottom'] > [data-popper-arrow] {
  top: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='left'] > [data-popper-arrow] {
  right: -5px;
}

[data-popover][role="tooltip"][data-popper-placement^='right'] > [data-popper-arrow] {
  left: -5px;
}

[role="tooltip"].invisible > [data-popper-arrow]:before {
  visibility: hidden;
}

[role="tooltip"].invisible > [data-popper-arrow]:after {
  visibility: hidden;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #1C64F2;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6B7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6B7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

input[type="time"]::-webkit-calendar-picker-indicator {
  background: none;
}

select:not([size]) {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 10 6%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m1 1 4 4 4-4%27/%3e %3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 0.75em 0.75em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

:is([dir=rtl]) select:not([size]) {
  background-position: left 0.75rem center;
  padding-right: 0.75rem;
  padding-left: 0;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #1C64F2;
  background-color: #fff;
  border-color: #6B7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #1C64F2;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked,.dark [type='checkbox']:checked,.dark [type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 0.55em 0.55em;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M1 5.917 5.724 10.5 15 1.5%27/%3e %3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

.dark [type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e");
  background-size: 1em 1em;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 12%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%273%27 d=%27M0.5 6h14%27/%3e %3c/svg%3e");
  background-color: currentColor;
  border-color: transparent;
  background-position: center;
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px auto inherit;
}

input[type=file]::file-selector-button {
  color: white;
  background: #1F2937;
  border: 0;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 2rem;
  padding-right: 1rem;
  margin-inline-start: -1rem;
  margin-inline-end: 1rem;
}

input[type=file]::file-selector-button:hover {
  background: #374151;
}

:is([dir=rtl]) input[type=file]::file-selector-button {
  padding-right: 2rem;
  padding-left: 1rem;
}

.dark input[type=file]::file-selector-button {
  color: white;
  background: #4B5563;
}

.dark input[type=file]::file-selector-button:hover {
  background: #6B7280;
}

input[type="range"]::-webkit-slider-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-webkit-slider-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-webkit-slider-thumb {
  background: #6B7280;
}

input[type="range"]:focus::-webkit-slider-thumb {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1px;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

input[type="range"]::-moz-range-thumb {
  height: 1.25rem;
  width: 1.25rem;
  background: #1C64F2;
  border-radius: 9999px;
  border: 0;
  appearance: none;
  -moz-appearance: none;
  -webkit-appearance: none;
  cursor: pointer;
}

input[type="range"]:disabled::-moz-range-thumb {
  background: #9CA3AF;
}

.dark input[type="range"]:disabled::-moz-range-thumb {
  background: #6B7280;
}

input[type="range"]::-moz-range-progress {
  background: #3F83F8;
}

input[type="range"]::-ms-fill-lower {
  background: #3F83F8;
}

.toggle-bg:after {
  content: "";
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  background: white;
  border-color: #D1D5DB;
  border-width: 1px;
  border-radius: 9999px;
  height: 1.25rem;
  width: 1.25rem;
  transition-property: background-color,border-color,color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
  transition-duration: .15s;
  box-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
}

input:checked + .toggle-bg:after {
  transform: translateX(100%);;
  border-color: white;
}

input:checked + .toggle-bg {
  background: #1C64F2;
  border-color: #1C64F2;
}
.container {
  width: 100%;
}
@media (min-width: 640px) {

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .container {
    max-width: 1536px;
  }
}
.selectedCell {
  background-color: #F9FAFB;
}
.dark .selectedCell {
  background-color: #374151;
}
.datatable-wrapper {
  width: 100%;
}
.datatable-wrapper .datatable-top {
  display: flex;
  justify-content: space-between;
  flex-direction: column-reverse;
  align-items: start;
  gap: 1rem;
  margin-bottom: 1rem;
}
.datatable-wrapper .datatable-search .datatable-input, .datatable-wrapper .datatable-input {
  color: #111827;
  font-size: 0.875rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.5rem;
  background-color: #F9FAFB;
  min-width: 16rem;
}
.dark .datatable-wrapper .datatable-search .datatable-input, .dark .datatable-wrapper .datatable-input {
  color: white;
  background-color: #1F2937;
  border: 1px solid #374151;
}
.datatable-wrapper thead th .datatable-input {
  background-color: white;
  font-weight: 400;
  color: #111827;
  padding-top: .35rem;
  padding-bottom: .35rem;
  min-width: 0;
}
.dark .datatable-wrapper thead th .datatable-input {
  background-color: #374151;
  border-color: #4B5563;
  color: white;
}
.datatable-wrapper .datatable-top .datatable-dropdown {
  color: #6B7280;
  font-size: 0.875rem;
}
.dark .datatable-wrapper .datatable-top .datatable-dropdown {
  color: #9CA3AF;
}
.datatable-wrapper .datatable-top .datatable-dropdown .datatable-selector {
  background-color: #F9FAFB;
  color: #111827;
  font-size: 0.875rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.5rem;
  margin-right: 0.25rem;
  min-width: 4rem;
}
.dark .datatable-wrapper .datatable-top .datatable-dropdown .datatable-selector {
  background-color: #1F2937;
  border: 1px solid #374151;
  color: white;
}
.datatable-wrapper .datatable-container thead tr.search-filtering-row th {
  padding-top: 0;
}
.datatable-wrapper .datatable-search .datatable-input:focus {
  border-color: #1C64F2;
}
.datatable-wrapper .datatable-container {
  overflow-x: auto;
}
.datatable-wrapper .datatable-table {
  width: 100%;
  font-size: 0.875rem;
  color: #6B7280;
  text-align: left;
}
.dark .datatable-wrapper .datatable-table {
  color: #9CA3AF;
}
.datatable-wrapper .datatable-table thead {
  font-size: 0.75rem;
  color: #6B7280;
  background-color: #F9FAFB;
}
.dark .datatable-wrapper .datatable-table thead {
  color: #9CA3AF;
  background-color: #1F2937;
}
.datatable-wrapper .datatable-table thead th {
  white-space: nowrap;
}
.datatable-wrapper .datatable-table thead th, .datatable-wrapper .datatable-table tbody th, .datatable-wrapper .datatable-table tbody td {
  width: auto !important;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.datatable-wrapper .datatable-table thead th .datatable-sorter, .datatable-wrapper .datatable-table thead th {
  text-transform: uppercase;
}
.datatable-wrapper .datatable-table thead th .datatable-sorter:hover, .datatable-wrapper .datatable-table thead th.datatable-ascending .datatable-sorter, .datatable-wrapper .datatable-table thead th.datatable-descending .datatable-sorter {
  color: #111827;
}
.dark .datatable-wrapper .datatable-table thead th .datatable-sorter:hover, .dark .datatable-wrapper .datatable-table thead th.datatable-ascending .datatable-sorter, .dark .datatable-wrapper .datatable-table thead th.datatable-descending .datatable-sorter {
  color: white;
}
.datatable-wrapper .datatable-table tbody tr.selected {
  background-color: #F3F4F6;
}
.dark .datatable-wrapper .datatable-table tbody tr.selected {
  background-color: #374151;
}
.datatable-wrapper .datatable-table tbody tr {
  border-bottom: 1px solid #E5E7EB;
}
.dark .datatable-wrapper .datatable-table tbody tr {
  border-bottom: 1px solid #374151;
}
.datatable-wrapper .datatable-table .datatable-empty {
  text-align: center;
}
.datatable-wrapper .datatable-bottom {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: start;
  margin-top: 1rem;
  gap: 1rem;
}
.datatable-wrapper .datatable-bottom .datatable-info {
  color: #6B7280;
  font-size: 0.875rem;
}
.dark .datatable-wrapper .datatable-bottom .datatable-info {
  color: #9CA3AF;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list {
  display: flex;
  align-items: center;
  height: 2rem;
  font-size: 0.875rem;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
  display: flex;
  align-items: center;
  color: #6B7280;
  font-weight: 500;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  height: 2rem;
  font-size: 0.875rem;
  border-top: 1px solid #D1D5DB;
  border-bottom: 1px solid #D1D5DB;
  border-right: 1px solid #D1D5DB;
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link {
  color: #9CA3AF;
  border-color: #374151;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type, .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type {
  position: relative;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link, .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
  color: transparent;
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link, .dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
  color: transparent;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1.3rem;
  height: 1.3rem;
  transform: translate(-50%, -50%);
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link:hover::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m14 8-4 4 4 4%27/%3e %3c/svg%3e");
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%236B7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
  position: absolute;
  top: 50%;
  right: 50%;
  width: 1.3rem;
  height: 1.3rem;
  transform: translate(50%, -50%);
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%23111827%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27%239CA3AF%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link:hover::after {
  content: url("data:image/svg+xml,%3csvg aria-hidden=%27true%27 xmlns=%27http://www.w3.org/2000/svg%27 width=%2720%27 height=%2720%27 fill=%27none%27 viewBox=%270 0 24 24%27%3e %3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27m10 16 4-4-4-4%27/%3e %3c/svg%3e");
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
  border-left: 1px solid #D1D5DB;
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:first-of-type .datatable-pagination-list-item-link {
  border-left: 1px solid #374151;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item:last-of-type .datatable-pagination-list-item-link {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  border-left: 0;
}
.datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
  background-color: #F9FAFB;
  color: #374151;
}
.dark .datatable-wrapper .datatable-bottom .datatable-pagination .datatable-pagination-list-item-link:hover {
  background-color: #374151;
  color: white;
}
@media (min-width: 640px) {

  .datatable-wrapper .datatable-top {
    flex-direction: row-reverse;
    align-items: center;
  }

  .datatable-wrapper .datatable-bottom {
    flex-direction: row;
    align-items: center;
  }
}
.apexcharts-canvas .apexcharts-tooltip {
  background-color: white;
  color: #6B7280;
  border: 0 !important;
  border-radius: 0.25rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}
.dark .apexcharts-canvas .apexcharts-tooltip {
  background-color: #374151;
  color: #9CA3AF;
  border-color: transparent;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}
.apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
  margin-bottom: 0.75rem;
  background-color: #F3F4F6;
  border-bottom-color: #E5E7EB;
  font-size: 0.875rem !important;
  font-weight: 400;
  color: #6B7280;
}
.dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-title {
  background-color: #4B5563;
  border-color: #6B7280;
  color: #9CA3AF;
}
.apexcharts-canvas .apexcharts-xaxistooltip {
  color: #6B7280;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-right: 0.75rem;
  padding-left: 0.75rem;
  border-color: transparent;
  background-color: white;
  border-radius: 0.25rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}
.dark .apexcharts-canvas .apexcharts-xaxistooltip {
  color: #9CA3AF;
  background-color: #374151;
}
.apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
  color: #6B7280;
  font-size: 0.875rem;
}
.dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-label {
  color: #9CA3AF;
}
.apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
  color: #111827;
  font-size: 0.875rem;
}
:is([dir=rtl]) .apexcharts-tooltip .apexcharts-tooltip-marker {
  margin-right: 0px;
  margin-left: e;
}
.dark .apexcharts-canvas .apexcharts-tooltip .apexcharts-tooltip-text-y-value {
  color: white;
}
.apexcharts-canvas .apexcharts-xaxistooltip-text {
  font-weight: 400;
  font-size: 0.875rem !important;
}
.apexcharts-canvas .apexcharts-xaxistooltip:after, .apexcharts-canvas .apexcharts-xaxistooltip:before {
  border-bottom-color: white;
}
.apexcharts-canvas .apexcharts-xaxistooltip:after {
  border-width: 8px;
  margin-left: -8px;
}
.apexcharts-canvas .apexcharts-xaxistooltip:before {
  border-width: 10px;
  margin-left: -10px;
}
.dark .apexcharts-canvas .apexcharts-xaxistooltip:after, .dark .apexcharts-canvas .apexcharts-xaxistooltip:before {
  border-bottom-color: #374151;
}
.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-y-group {
  padding: 0;
}
.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-bottom: 0.75rem;
  background-color: white !important;
  color: #6B7280 !important;
}
.dark .apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active {
  background-color: #374151 !important;
  color: #9CA3AF !important;
}
.apexcharts-canvas .apexcharts-tooltip-series-group.apexcharts-active:first-of-type {
  padding-top: 0.75rem;
}
.apexcharts-canvas .apexcharts-legend {
  padding: 0 !important;
}
.apexcharts-canvas .apexcharts-legend-text {
  font-size: 0.75rem;
  font-weight: 500 !important;
  padding-left: 1.25rem;
  color: #6B7280 !important;
}
:is([dir=rtl]) .apexcharts-canvas .apexcharts-legend-text {
  padding-right: 0.5rem;
}
.apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
  color: #111827 !important;
}
.dark .apexcharts-canvas .apexcharts-legend-text {
  color: #9CA3AF !important;
}
.dark .apexcharts-canvas .apexcharts-legend-text:not(.apexcharts-inactive-legend):hover {
  color: white !important;
}
.apexcharts-canvas .apexcharts-legend-series {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  margin-bottom: 0.25rem !important;
  display: flex;
  align-items: center;
}
.apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  fill: #111827 !important;
  font-size: 1.875rem;
  font-weight: 700;
}
.dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  fill: white !important;
}
.apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  fill: #6B7280 !important;
  font-size: 1rem;
  font-weight: 400;
}
.dark .apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  fill: #9CA3AF !important;
}
.apexcharts-canvas .apexcharts-datalabels .apexcharts-text.apexcharts-pie-label {
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  text-shadow: none !important;
  filter: none !important;
}
.apexcharts-gridline, .apexcharts-xcrosshairs, .apexcharts-ycrosshairs {
  stroke: #E5E7EB !important;
}
.dark .apexcharts-gridline, .dark .apexcharts-xcrosshairs, .dark .apexcharts-ycrosshairs {
  stroke: #374151 !important;
}
.format {
  color: var(--tw-format-body);
  max-width: 65ch;
}
.format :where([class~="lead"]):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}
.format :where(a):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-links);
  text-decoration: underline;
  font-weight: 500;
}
.format :where(a):not(:where([class~="not-format"] *)):hover {
  text-decoration: none;
}
.format :where(strong):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-bold);
  font-weight: 700;
}
.format :where(a strong):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(blockquote strong):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(thead th strong):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(ol):not(:where([class~="not-format"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}
.format :where(ol[type="A"]):not(:where([class~="not-format"] *)) {
  list-style-type: upper-alpha;
}
.format :where(ol[type="a"]):not(:where([class~="not-format"] *)) {
  list-style-type: lower-alpha;
}
.format :where(ol[type="A" s]):not(:where([class~="not-format"] *)) {
  list-style-type: upper-alpha;
}
.format :where(ol[type="a" s]):not(:where([class~="not-format"] *)) {
  list-style-type: lower-alpha;
}
.format :where(ol[type="I"]):not(:where([class~="not-format"] *)) {
  list-style-type: upper-roman;
}
.format :where(ol[type="i"]):not(:where([class~="not-format"] *)) {
  list-style-type: lower-roman;
}
.format :where(ol[type="I" s]):not(:where([class~="not-format"] *)) {
  list-style-type: upper-roman;
}
.format :where(ol[type="i" s]):not(:where([class~="not-format"] *)) {
  list-style-type: lower-roman;
}
.format :where(ol[type="1"]):not(:where([class~="not-format"] *)) {
  list-style-type: decimal;
}
.format :where(ul):not(:where([class~="not-format"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}
.format :where(ol > li):not(:where([class~="not-format"] *))::marker {
  font-weight: 400;
  color: var(--tw-format-counters);
}
.format :where(ul > li):not(:where([class~="not-format"] *))::marker {
  color: var(--tw-format-bullets);
}
.format :where(hr):not(:where([class~="not-format"] *)) {
  border-color: var(--tw-format-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}
.format :where(blockquote):not(:where([class~="not-format"] *)) {
  font-size: 1.1111111em;
  font-weight: 700;
  font-style: italic;
  color: var(--tw-format-quotes);
  quotes: "\201C""\201D""\2018""\2019";
  margin-bottom: 1.6em;
}
.format :where(blockquote):not(:where([class~="not-format"] *))::before {
  content: "";
  background-image: url("data:image/svg+xml,%0A%3Csvg width=%2732%27 height=%2724%27 viewBox=%270 0 32 24%27 fill=%27none%27 xmlns=%27http://www.w3.org/2000/svg%27%3E%3Cpath d=%27M18.6893 24V14.1453C18.6893 6.54 23.664 1.38533 30.6667 -7.15256e-07L31.9933 2.868C28.7507 4.09066 26.6667 7.71867 26.6667 10.6667H32V24H18.6893ZM-9.53674e-07 24V14.1453C-9.53674e-07 6.54 4.99733 1.384 12 -7.15256e-07L13.328 2.868C10.084 4.09066 8 7.71867 8 10.6667L13.3107 10.6667V24H-9.53674e-07Z%27 fill=%27%239CA3AF%27/%3E%3C/svg%3E%0A");
  background-repeat: no-repeat;
  color: var(--tw-format-quotes);
  width: 1.7777778em;
  height: 1.3333333em;
  display: block;
  margin-top: 1.6em;
}
.format :where(blockquote p:first-of-type):not(:where([class~="not-format"] *))::before {
  content: open-quote;
}
.format :where(blockquote p:last-of-type):not(:where([class~="not-format"] *))::after {
  content: close-quote;
}
.format :where(h1):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}
.format :where(h1 strong):not(:where([class~="not-format"] *)) {
  font-weight: 900;
  color: inherit;
}
.format :where(h2):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 0;
  margin-bottom: 1em;
  line-height: 1.3333333;
}
.format :where(h2 strong):not(:where([class~="not-format"] *)) {
  font-weight: 800;
  color: inherit;
}
.format :where(h3):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-headings);
  font-weight: 700;
  font-size: 1.25em;
  margin-top: 0;
  margin-bottom: 0.6em;
  line-height: 1.6;
}
.format :where(h3 strong):not(:where([class~="not-format"] *)) {
  font-weight: 800;
  color: inherit;
}
.format :where(h4):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-headings);
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.5em;
  line-height: 1.5;
}
.format :where(h4 strong):not(:where([class~="not-format"] *)) {
  font-weight: 700;
  color: inherit;
}
.format :where(img):not(:where([class~="not-format"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.format :where(figure > *):not(:where([class~="not-format"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}
.format :where(figcaption):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}
.format :where(code):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-code);
  font-weight: 600;
  background-color: var(--tw-format-code-bg);
  padding-top: 0.3333333em;
  padding-bottom: 0.3333333em;
  padding-left: 0.5555556em;
  padding-right: 0.5555556em;
  border-radius: 0.2222222em;
  font-size: 0.875em;
}
.format :where(a code):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(h1 code):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(h2 code):not(:where([class~="not-format"] *)) {
  color: inherit;
  font-size: 0.875em;
}
.format :where(h3 code):not(:where([class~="not-format"] *)) {
  color: inherit;
  font-size: 0.9em;
}
.format :where(h4 code):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(blockquote code):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(thead th code):not(:where([class~="not-format"] *)) {
  color: inherit;
}
.format :where(pre):not(:where([class~="not-format"] *)) {
  color: var(--tw-format-pre-code);
  background-color: var(--tw-format-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-right: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-left: 1.1428571em;
}
.format :where(pre code):not(:where([class~="not-format"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}
.format :where(pre code):not(:where([class~="not-format"] *))::before {
  content: none;
}
.format :where(pre code):not(:where([class~="not-format"] *))::after {
  content: none;
}
.format :where(table):not(:where([class~="not-format"] *)) {
  width: 100%;
  table-layout: auto;
  text-align: left;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}
.format :where(thead):not(:where([class~="not-format"] *)) {
  background-color: var(--tw-format-th-bg);
  border-radius: 0.2777778em;
}
.format :where(thead th):not(:where([class~="not-format"] *)) {
  background-color: var(--tw-format-th-bg);
  color: var(--tw-format-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding: 0.5555556em;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}
.format :where(tbody th):not(:where([class~="not-format"] *)) {
  background-color: var(--tw-format-th-bg);
  color: var(--tw-format-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding: 0.5555556em;
}
.format :where(tbody tr th p, tbody tr td p):not(:where([class~="not-format"] *)) {
  margin: 0 !important;
}
.format :where(tbody tr th, tbody tr td):not(:where([class~="not-format"] *)) {
  padding: 0.6666667em !important;
}
.format :where(tbody tr):not(:where([class~="not-format"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-format-td-borders);
}
.format :where(tbody tr:last-child):not(:where([class~="not-format"] *)) {
  border-bottom-width: 0;
}
.format :where(tbody td):not(:where([class~="not-format"] *)) {
  vertical-align: baseline;
}
.format :where(tfoot):not(:where([class~="not-format"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-format-th-borders);
}
.format :where(tfoot td):not(:where([class~="not-format"] *)) {
  vertical-align: top;
}
.format {
  --tw-format-body: #6b7280;
  --tw-format-headings: #111827;
  --tw-format-lead: #6b7280;
  --tw-format-links: #4b5563;
  --tw-format-bold: #111827;
  --tw-format-counters: #6b7280;
  --tw-format-bullets: #6b7280;
  --tw-format-hr: #e5e7eb;
  --tw-format-quotes: #111827;
  --tw-format-quote-borders: #e5e7eb;
  --tw-format-captions: #6b7280;
  --tw-format-code: #111827;
  --tw-format-code-bg: #f3f4f6;
  --tw-format-pre-code: #4b5563;
  --tw-format-pre-bg: #f3f4f6;
  --tw-format-th-borders: #e5e7eb;
  --tw-format-th-bg: #f9fafb;
  --tw-format-td-borders: #e5e7eb;
  --tw-format-invert-body: #9ca3af;
  --tw-format-invert-headings: #fff;
  --tw-format-invert-lead: #9ca3af;
  --tw-format-invert-links: #fff;
  --tw-format-invert-bold: #fff;
  --tw-format-invert-counters: #9ca3af;
  --tw-format-invert-bullets: #4b5563;
  --tw-format-invert-hr: #374151;
  --tw-format-invert-quotes: #f3f4f6;
  --tw-format-invert-quote-borders: #374151;
  --tw-format-invert-captions: #9ca3af;
  --tw-format-invert-code: #fff;
  --tw-format-invert-code-bg: #1f2937;
  --tw-format-invert-pre-code: #d1d5db;
  --tw-format-invert-pre-bg: #374151;
  --tw-format-invert-th-borders: #4b5563;
  --tw-format-invert-td-borders: #374151;
  --tw-format-invert-th-bg: #374151;
  font-size: 1rem;
  line-height: 1.75;
}
.format :where(p):not(:where([class~="not-format"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}
.format :where(blockquote > p:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(video):not(:where([class~="not-format"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.format :where(figure):not(:where([class~="not-format"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}
.format :where(li):not(:where([class~="not-format"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.format :where(ol > li):not(:where([class~="not-format"] *)) {
  padding-left: 0.375em;
}
.format :where(ul > li):not(:where([class~="not-format"] *)) {
  padding-left: 0.375em;
}
.format :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.format :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.25em;
}
.format :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.25em;
}
.format :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.25em;
}
.format :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.25em;
}
.format :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-format"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.format :where(hr + *):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(h2 + *):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(h3 + *):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(h4 + *):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(thead th:last-child):not(:where([class~="not-format"] *)) {
  padding-right: 0;
}
.format :where(tbody td, tfoot td):not(:where([class~="not-format"] *)) {
  padding-top: 0.5714286em;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}
.format :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-format"] *)) {
  padding-right: 0;
}
.format :where(.format > :first-child):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format :where(.format > :last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 0;
}
.format-sm :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}
.format-sm :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1em;
}
.format-sm :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1em;
}
.format-sm :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1em;
}
.format-sm :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1em;
}
.format-sm :where(.format > :first-child):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format-sm :where(.format > :last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 0;
}
.format-base :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}
.format-base :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.25em;
}
.format-base :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.25em;
}
.format-base :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.25em;
}
.format-base :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.25em;
}
.format-base :where(.format > :first-child):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format-base :where(.format > :last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 0;
}
.format-lg :where(.format > ul > li p):not(:where([class~="not-format"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}
.format-lg :where(.format > ul > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.3333333em;
}
.format-lg :where(.format > ul > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.3333333em;
}
.format-lg :where(.format > ol > li > *:first-child):not(:where([class~="not-format"] *)) {
  margin-top: 1.3333333em;
}
.format-lg :where(.format > ol > li > *:last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 1.3333333em;
}
.format-lg :where(.format > :first-child):not(:where([class~="not-format"] *)) {
  margin-top: 0;
}
.format-lg :where(.format > :last-child):not(:where([class~="not-format"] *)) {
  margin-bottom: 0;
}
.format-blue {
  --tw-format-links: #2563eb;
  --tw-format-invert-links: #3b82f6;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.\!visible {
  visibility: visible !important;
}
.visible {
  visibility: visible;
}
.invisible {
  visibility: hidden;
}
.collapse {
  visibility: collapse;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-6 {
  bottom: -1.5rem;
}
.-start-1\.5 {
  inset-inline-start: -0.375rem;
}
.-start-3 {
  inset-inline-start: -0.75rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1 {
  bottom: 0.25rem;
}
.bottom-2\.5 {
  bottom: 0.625rem;
}
.bottom-3 {
  bottom: 0.75rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-5 {
  bottom: 1.25rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-\[60px\] {
  bottom: 60px;
}
.end-0 {
  inset-inline-end: 0px;
}
.end-1 {
  inset-inline-end: 0.25rem;
}
.end-2 {
  inset-inline-end: 0.5rem;
}
.end-2\.5 {
  inset-inline-end: 0.625rem;
}
.end-4 {
  inset-inline-end: 1rem;
}
.left-0 {
  left: 0px;
}
.left-1 {
  left: 0.25rem;
}
.left-1\/2 {
  left: 50%;
}
.left-2 {
  left: 0.5rem;
}
.left-4 {
  left: 1rem;
}
.left-5 {
  left: 1.25rem;
}
.left-6 {
  left: 1.5rem;
}
.right-0 {
  right: 0px;
}
.right-2 {
  right: 0.5rem;
}
.right-2\.5 {
  right: 0.625rem;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.start-0 {
  inset-inline-start: 0px;
}
.start-1\/3 {
  inset-inline-start: 33.333333%;
}
.start-2\/3 {
  inset-inline-start: 66.666667%;
}
.start-4 {
  inset-inline-start: 1rem;
}
.start-6 {
  inset-inline-start: 1.5rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-2 {
  top: 0.5rem;
}
.top-2\.5 {
  top: 0.625rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.order-1 {
  order: 1;
}
.order-2 {
  order: 2;
}
.order-3 {
  order: 3;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.col-span-12 {
  grid-column: span 12 / span 12;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-3 {
  grid-column: span 3 / span 3;
}
.col-span-6 {
  grid-column: span 6 / span 6;
}
.col-span-full {
  grid-column: 1 / -1;
}
.m-0 {
  margin: 0px;
}
.m-0\.5 {
  margin: 0.125rem;
}
.m-1 {
  margin: 0.25rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-1\.5 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-5 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-0\.5 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-2\.5 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.-mb-px {
  margin-bottom: -1px;
}
.-me-0\.5 {
  margin-inline-end: -0.125rem;
}
.-me-1 {
  margin-inline-end: -0.25rem;
}
.-ml-0\.5 {
  margin-left: -0.125rem;
}
.-ml-1 {
  margin-left: -0.25rem;
}
.-mr-1 {
  margin-right: -0.25rem;
}
.-mr-3 {
  margin-right: -0.75rem;
}
.-ms-0\.5 {
  margin-inline-start: -0.125rem;
}
.-ms-1 {
  margin-inline-start: -0.25rem;
}
.-ms-2 {
  margin-inline-start: -0.5rem;
}
.-mt-1 {
  margin-top: -0.25rem;
}
.-mt-5 {
  margin-top: -1.25rem;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-1\.5 {
  margin-bottom: 0.375rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-2\.5 {
  margin-bottom: 0.625rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-3\.5 {
  margin-bottom: 0.875rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.me-1 {
  margin-inline-end: 0.25rem;
}
.me-1\.5 {
  margin-inline-end: 0.375rem;
}
.me-2 {
  margin-inline-end: 0.5rem;
}
.me-2\.5 {
  margin-inline-end: 0.625rem;
}
.me-3 {
  margin-inline-end: 0.75rem;
}
.me-4 {
  margin-inline-end: 1rem;
}
.me-6 {
  margin-inline-end: 1.5rem;
}
.me-8 {
  margin-inline-end: 2rem;
}
.me-auto {
  margin-inline-end: auto;
}
.ml-0 {
  margin-left: 0px;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-1\.5 {
  margin-left: 0.375rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-6 {
  margin-left: 1.5rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-0\.5 {
  margin-right: 0.125rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-1\.5 {
  margin-right: 0.375rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-4 {
  margin-right: 1rem;
}
.mr-auto {
  margin-right: auto;
}
.ms-0\.5 {
  margin-inline-start: 0.125rem;
}
.ms-1 {
  margin-inline-start: 0.25rem;
}
.ms-1\.5 {
  margin-inline-start: 0.375rem;
}
.ms-2 {
  margin-inline-start: 0.5rem;
}
.ms-3 {
  margin-inline-start: 0.75rem;
}
.ms-4 {
  margin-inline-start: 1rem;
}
.ms-5 {
  margin-inline-start: 1.25rem;
}
.ms-6 {
  margin-inline-start: 1.5rem;
}
.ms-auto {
  margin-inline-start: auto;
}
.mt-0 {
  margin-top: 0px;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-1\.5 {
  margin-top: 0.375rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-2\.5 {
  margin-top: 0.625rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.table-column {
  display: table-column;
}
.flow-root {
  display: flow-root;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-11 {
  height: 2.75rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-36 {
  height: 9rem;
}
.h-4 {
  height: 1rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-56 {
  height: 14rem;
}
.h-6 {
  height: 1.5rem;
}
.h-60 {
  height: 15rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-9 {
  height: 2.25rem;
}
.h-\[78px\] {
  height: 78px;
}
.h-\[calc\(100\%-1rem\)\] {
  height: calc(100% - 1rem);
}
.h-\[calc\(100vh-13rem\)\] {
  height: calc(100vh - 13rem);
}
.h-\[calc\(100vh-15\.8rem\)\] {
  height: calc(100vh - 15.8rem);
}
.h-\[calc\(100vh-16rem\)\] {
  height: calc(100vh - 16rem);
}
.h-\[calc\(100vh-18\.5rem\)\] {
  height: calc(100vh - 18.5rem);
}
.h-\[calc\(100vh-18\.7rem\)\] {
  height: calc(100vh - 18.7rem);
}
.h-\[calc\(100vh-19\.4rem\)\] {
  height: calc(100vh - 19.4rem);
}
.h-\[calc\(100vh-4rem\)\] {
  height: calc(100vh - 4rem);
}
.h-\[calc\(100vh-7rem\)\] {
  height: calc(100vh - 7rem);
}
.h-\[calc\(100vh-8rem\)\] {
  height: calc(100vh - 8rem);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-modal {
  height: calc(100% - 2rem);
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.h-svh {
  height: 100svh;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-full {
  max-height: 100%;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/2 {
  width: 50%;
}
.w-10 {
  width: 2.5rem;
}
.w-11 {
  width: 2.75rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/5 {
  width: 40%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-28 {
  width: 7rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-32 {
  width: 8rem;
}
.w-36 {
  width: 9rem;
}
.w-4 {
  width: 1rem;
}
.w-40 {
  width: 10rem;
}
.w-44 {
  width: 11rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-52 {
  width: 13rem;
}
.w-56 {
  width: 14rem;
}
.w-6 {
  width: 1.5rem;
}
.w-60 {
  width: 15rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[106px\] {
  width: 106px;
}
.w-\[145px\] {
  width: 145px;
}
.w-\[340px\] {
  width: 340px;
}
.w-\[356px\] {
  width: 356px;
}
.w-\[360px\] {
  width: 360px;
}
.w-\[512px\] {
  width: 512px;
}
.w-\[calc\(100\%-2rem\)\] {
  width: calc(100% - 2rem);
}
.w-auto {
  width: auto;
}
.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-32 {
  min-width: 8rem;
}
.min-w-40 {
  min-width: 10rem;
}
.min-w-64 {
  min-width: 16rem;
}
.min-w-80 {
  min-width: 20rem;
}
.min-w-\[10rem\] {
  min-width: 10rem;
}
.min-w-\[12rem\] {
  min-width: 12rem;
}
.min-w-\[14rem\] {
  min-width: 14rem;
}
.min-w-\[16rem\] {
  min-width: 16rem;
}
.min-w-\[6rem\] {
  min-width: 6rem;
}
.min-w-\[7rem\] {
  min-width: 7rem;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.min-w-full {
  min-width: 100%;
}
.min-w-kanban {
  min-width: 28rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-52 {
  max-width: 13rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-64 {
  max-width: 16rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-80 {
  max-width: 20rem;
}
.max-w-\[12rem\] {
  max-width: 12rem;
}
.max-w-\[14rem\] {
  max-width: 14rem;
}
.max-w-\[2\.5rem\] {
  max-width: 2.5rem;
}
.max-w-\[320px\] {
  max-width: 320px;
}
.max-w-\[404px\] {
  max-width: 404px;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-screen-2xl {
  max-width: 1536px;
}
.max-w-screen-lg {
  max-width: 1024px;
}
.max-w-screen-md {
  max-width: 768px;
}
.max-w-screen-xl {
  max-width: 1280px;
}
.max-w-sm {
  max-width: 24rem;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-none {
  flex: none;
}
.flex-shrink {
  flex-shrink: 1;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.grow {
  flex-grow: 1;
}
.table-fixed {
  table-layout: fixed;
}
.origin-\[0\] {
  transform-origin: 0;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-6 {
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-75 {
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none {
  transform: none;
}
.cursor-default {
  cursor: default;
}
.cursor-move {
  cursor: move;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.resize {
  resize: both;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-col {
  flex-direction: column;
}
.flex-col-reverse {
  flex-direction: column-reverse;
}
.flex-wrap {
  flex-wrap: wrap;
}
.place-items-center {
  place-items: center;
}
.content-center {
  align-content: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-items-center {
  justify-items: center;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-16 {
  gap: 4rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-4 {
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-4 {
  row-gap: 1rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.-space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1rem * var(--tw-space-x-reverse));
  margin-left: calc(-1rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0px * var(--tw-space-x-reverse));
  margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.125rem * var(--tw-space-x-reverse));
  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.375rem * var(--tw-space-x-reverse));
  margin-left: calc(0.375rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.625rem * var(--tw-space-x-reverse));
  margin-left: calc(0.625rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.25rem * var(--tw-space-x-reverse));
  margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.divide-x > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 0;
  border-right-width: calc(1px * var(--tw-divide-x-reverse));
  border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity));
}
.place-self-center {
  place-self: center;
}
.self-center {
  align-self: center;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-scroll {
  overflow: scroll;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.overflow-x-scroll {
  overflow-x: scroll;
}
.overflow-y-scroll {
  overflow-y: scroll;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre {
  white-space: pre;
}
.text-nowrap {
  text-wrap: nowrap;
}
.break-all {
  word-break: break-all;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.rounded-b-lg {
  border-bottom-right-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-e-lg {
  border-start-end-radius: 0.5rem;
  border-end-end-radius: 0.5rem;
}
.rounded-e-xl {
  border-start-end-radius: 0.75rem;
  border-end-end-radius: 0.75rem;
}
.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}
.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}
.rounded-s-lg {
  border-start-start-radius: 0.5rem;
  border-end-start-radius: 0.5rem;
}
.rounded-s-xl {
  border-start-start-radius: 0.75rem;
  border-end-start-radius: 0.75rem;
}
.rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.rounded-t-lg {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
.rounded-ee-xl {
  border-end-end-radius: 0.75rem;
}
.rounded-es-xl {
  border-end-start-radius: 0.75rem;
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-x {
  border-left-width: 1px;
  border-right-width: 1px;
}
.border-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-e {
  border-inline-end-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-0 {
  border-right-width: 0px;
}
.border-s {
  border-inline-start-width: 1px;
}
.border-s-2 {
  border-inline-start-width: 2px;
}
.border-t {
  border-top-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.\!border-gray-100 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(243 244 246 / var(--tw-border-opacity)) !important;
}
.\!border-primary-700 {
  --tw-border-opacity: 1 !important;
  border-color: rgb(29 78 216 / var(--tw-border-opacity)) !important;
}
.border-blue-600 {
  --tw-border-opacity: 1;
  border-color: rgb(28 100 242 / var(--tw-border-opacity));
}
.border-blue-700 {
  --tw-border-opacity: 1;
  border-color: rgb(26 86 219 / var(--tw-border-opacity));
}
.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}
.border-gray-50 {
  --tw-border-opacity: 1;
  border-color: rgb(249 250 251 / var(--tw-border-opacity));
}
.border-green-300 {
  --tw-border-opacity: 1;
  border-color: rgb(132 225 188 / var(--tw-border-opacity));
}
.border-orange-200 {
  --tw-border-opacity: 1;
  border-color: rgb(252 217 189 / var(--tw-border-opacity));
}
.border-primary-300 {
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity));
}
.border-primary-600 {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}
.border-primary-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
}
.border-red-600 {
  --tw-border-opacity: 1;
  border-color: rgb(224 36 36 / var(--tw-border-opacity));
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(200 30 30 / var(--tw-border-opacity));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-s-gray-100 {
  --tw-border-opacity: 1;
  border-inline-start-color: rgb(243 244 246 / var(--tw-border-opacity));
}
.border-s-gray-50 {
  --tw-border-opacity: 1;
  border-inline-start-color: rgb(249 250 251 / var(--tw-border-opacity));
}
.\!bg-gray-50 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
}
.\!bg-primary-600 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity)) !important;
}
.\!bg-primary-700 {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}
.bg-\[\#4285F4\] {
  --tw-bg-opacity: 1;
  background-color: rgb(66 133 244 / var(--tw-bg-opacity));
}
.bg-blue-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity));
}
.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}
.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.bg-gray-900\/50 {
  background-color: rgb(17 24 39 / 0.5);
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity));
}
.bg-green-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(132 225 188 / var(--tw-bg-opacity));
}
.bg-green-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(49 196 141 / var(--tw-bg-opacity));
}
.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 250 247 / var(--tw-bg-opacity));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}
.bg-indigo-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 237 255 / var(--tw-bg-opacity));
}
.bg-indigo-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(141 162 251 / var(--tw-bg-opacity));
}
.bg-indigo-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(104 117 245 / var(--tw-bg-opacity));
}
.bg-indigo-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(88 80 236 / var(--tw-bg-opacity));
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 236 220 / var(--tw-bg-opacity));
}
.bg-orange-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 140 / var(--tw-bg-opacity));
}
.bg-orange-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 138 76 / var(--tw-bg-opacity));
}
.bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 248 241 / var(--tw-bg-opacity));
}
.bg-orange-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 90 31 / var(--tw-bg-opacity));
}
.bg-pink-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 232 243 / var(--tw-bg-opacity));
}
.bg-pink-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(241 126 184 / var(--tw-bg-opacity));
}
.bg-pink-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(231 70 148 / var(--tw-bg-opacity));
}
.bg-pink-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(214 31 105 / var(--tw-bg-opacity));
}
.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}
.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}
.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}
.bg-primary-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}
.bg-purple-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 235 254 / var(--tw-bg-opacity));
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(246 245 255 / var(--tw-bg-opacity));
}
.bg-purple-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(144 97 249 / var(--tw-bg-opacity));
}
.bg-purple-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 58 242 / var(--tw-bg-opacity));
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}
.bg-red-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}
.bg-red-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}
.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity));
}
.bg-teal-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(126 220 226 / var(--tw-bg-opacity));
}
.bg-teal-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(22 189 202 / var(--tw-bg-opacity));
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(237 250 250 / var(--tw-bg-opacity));
}
.bg-teal-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(4 116 129 / var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}
.bg-yellow-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity));
}
.bg-yellow-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 202 21 / var(--tw-bg-opacity));
}
.bg-opacity-5 {
  --tw-bg-opacity: 0.05;
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.bg-opacity-75 {
  --tw-bg-opacity: 0.75;
}
.bg-\[url\(\'https\:\/\/flowbite\.s3\.amazonaws\.com\/blocks\/e-commerce\/gaming-image\.jpg\'\)\] {
  background-image: url('https://flowbite.s3.amazonaws.com/blocks/e-commerce/gaming-image.jpg');
}
.bg-contain {
  background-size: contain;
}
.bg-cover {
  background-size: cover;
}
.bg-center {
  background-position: center;
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.fill-gray-500 {
  fill: #6B7280;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-2\.5 {
  padding: 0.625rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-3\.5 {
  padding: 0.875rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-px {
  padding: 1px;
}
.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.\!pe-6 {
  padding-inline-end: 1.5rem !important;
}
.pb-0 {
  padding-bottom: 0px;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-2\.5 {
  padding-bottom: 0.625rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pb-6 {
  padding-bottom: 1.5rem;
}
.pe-1 {
  padding-inline-end: 0.25rem;
}
.pe-10 {
  padding-inline-end: 2.5rem;
}
.pe-14 {
  padding-inline-end: 3.5rem;
}
.pe-3 {
  padding-inline-end: 0.75rem;
}
.pe-3\.5 {
  padding-inline-end: 0.875rem;
}
.pe-4 {
  padding-inline-end: 1rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-5 {
  padding-left: 1.25rem;
}
.pl-9 {
  padding-left: 2.25rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-5 {
  padding-right: 1.25rem;
}
.ps-1\.5 {
  padding-inline-start: 0.375rem;
}
.ps-10 {
  padding-inline-start: 2.5rem;
}
.ps-12 {
  padding-inline-start: 3rem;
}
.ps-2 {
  padding-inline-start: 0.5rem;
}
.ps-3 {
  padding-inline-start: 0.75rem;
}
.ps-3\.5 {
  padding-inline-start: 0.875rem;
}
.ps-4 {
  padding-inline-start: 1rem;
}
.ps-9 {
  padding-inline-start: 2.25rem;
}
.ps-\[38px\] {
  padding-inline-start: 38px;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-2\.5 {
  padding-top: 0.625rem;
}
.pt-20 {
  padding-top: 5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.pt-\[62px\] {
  padding-top: 62px;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-start {
  text-align: start;
}
.text-end {
  text-align: end;
}
.align-middle {
  vertical-align: middle;
}
.font-sans {
  font-family: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Noto Color Emoji;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-5xl {
  font-size: 3rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-normal {
  font-weight: 400;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.lowercase {
  text-transform: lowercase;
}
.italic {
  font-style: italic;
}
.leading-6 {
  line-height: 1.5rem;
}
.leading-9 {
  line-height: 2.25rem;
}
.leading-none {
  line-height: 1;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}
.text-\[\#1434CB\] {
  --tw-text-opacity: 1;
  color: rgb(20 52 203 / var(--tw-text-opacity));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}
.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(5 122 85 / var(--tw-text-opacity));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(4 108 78 / var(--tw-text-opacity));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(3 84 63 / var(--tw-text-opacity));
}
.text-indigo-700 {
  --tw-text-opacity: 1;
  color: rgb(81 69 205 / var(--tw-text-opacity));
}
.text-indigo-800 {
  --tw-text-opacity: 1;
  color: rgb(66 56 157 / var(--tw-text-opacity));
}
.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(255 138 76 / var(--tw-text-opacity));
}
.text-orange-600 {
  --tw-text-opacity: 1;
  color: rgb(208 56 1 / var(--tw-text-opacity));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(180 52 3 / var(--tw-text-opacity));
}
.text-pink-700 {
  --tw-text-opacity: 1;
  color: rgb(191 18 93 / var(--tw-text-opacity));
}
.text-primary-100 {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}
.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
.text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}
.text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}
.text-primary-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}
.text-purple-600 {
  --tw-text-opacity: 1;
  color: rgb(126 58 242 / var(--tw-text-opacity));
}
.text-purple-700 {
  --tw-text-opacity: 1;
  color: rgb(108 43 217 / var(--tw-text-opacity));
}
.text-purple-800 {
  --tw-text-opacity: 1;
  color: rgb(85 33 181 / var(--tw-text-opacity));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(224 36 36 / var(--tw-text-opacity));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(200 30 30 / var(--tw-text-opacity));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(155 28 28 / var(--tw-text-opacity));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(4 116 129 / var(--tw-text-opacity));
}
.text-teal-700 {
  --tw-text-opacity: 1;
  color: rgb(3 102 114 / var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-yellow-300 {
  --tw-text-opacity: 1;
  color: rgb(250 202 21 / var(--tw-text-opacity));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(227 160 8 / var(--tw-text-opacity));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(194 120 3 / var(--tw-text-opacity));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgb(142 75 16 / var(--tw-text-opacity));
}
.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(114 59 19 / var(--tw-text-opacity));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.bg-blend-multiply {
  background-blend-mode: multiply;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline {
  outline-style: solid;
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-8 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(8px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-transparent {
  --tw-ring-color: transparent;
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.\[appearance\:textfield\] {
  -webkit-appearance: textfield;
     -moz-appearance: textfield;
          appearance: textfield;
}

/* chart styles */
.apexcharts-tooltip {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.apexcharts-tooltip:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.apexcharts-tooltip .apexcharts-tooltip-title {
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
  padding-left: 1rem !important;
  padding-right: 1rem !important;
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.apexcharts-tooltip .apexcharts-tooltip-title:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(107 114 128 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity)) !important;
}

.apexcharts-xaxistooltip {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.apexcharts-xaxistooltip:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(209 213 219 / var(--tw-text-opacity)) !important;
}

.apexcharts-tooltip .apexcharts-tooltip-text-y-value:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.apexcharts-xaxistooltip-text {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
}

.apexcharts-xaxistooltip:before,
.apexcharts-xaxistooltip:after {
  border-width: 0px !important;
}

.apexcharts-canvas .apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-label {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 500;
}

.apexcharts-datalabels-group .apexcharts-text.apexcharts-datalabel-value {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 800;
}

/* SVG map styles */
.svgMap-map-wrapper {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}

.svgMap-map-image:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.svgMap-map-controls-wrapper {
  bottom: 0px !important;
  left: 0px !important;
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.svgMap-map-controls-wrapper:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

.svgMap-map-controls-zoom:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

.svgMap-map-wrapper .svgMap-control-button {
  border-radius: 0.5rem !important;
  border-width: 1px !important;
  border-style: solid !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(209 213 219 / var(--tw-border-opacity)) !important;
}

.svgMap-map-wrapper .svgMap-control-button:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
}

.svgMap-map-wrapper .svgMap-control-button:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(75 85 99 / var(--tw-border-opacity)) !important;
}

.svgMap-map-wrapper .svgMap-control-button:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity)) !important;
}

.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:is(.dark *):after,
.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:is(.dark *):before {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:is(.dark *):after,
.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:is(.dark *):before {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.svgMap-map-wrapper .svgMap-control-button:first-child {
  margin-right: 0.5rem !important;
}

.svgMap-tooltip {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  text-align: left !important;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1) !important;
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color) !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.svgMap-tooltip:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container {
  margin-right: 0.5rem !important;
  display: inline-block !important;
  border-width: 0px !important;
  padding: 0px !important;
  text-align: left !important;
}

.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container .svgMap-tooltip-flag {
  display: inline-block !important;
  height: 1rem !important;
  border-width: 0px !important;
  padding: 0px !important;
}

.svgMap-tooltip .svgMap-tooltip-title {
  display: inline-block !important;
  padding-top: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 600 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-title:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-content {
  margin-top: 0px !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td {
  text-align: left !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 400 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td span {
  text-align: left !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 600 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td span:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.svgMap-tooltip .svgMap-tooltip-pointer {
  display: none !important;
}

.svgMap-map-wrapper .svgMap-country:is(.dark *) {
  stroke: #1F2937;
}

/* kanban styles */

.drag-card {
  opacity: 1 !important;
  --tw-rotate: 6deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.ghost-card {
  background-color: rgb(243 244 246 / 0.4) !important;
}

.ghost-card:is(.dark *) {
  background-color: rgb(75 85 99 / 0.4) !important;
}

/* calendar styles */

.fc .fc-toolbar {
  padding-left: 1rem !important;
  padding-right: 1rem !important;
}

.fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 0px !important;
  flex-direction: column !important;
}

.fc .fc-toolbar.fc-header-toolbar > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse)) !important;
}

.fc .fc-toolbar.fc-header-toolbar {
  padding-bottom: 1rem !important;
  padding-top: 1rem !important;
}

.fc .fc-toolbar.fc-header-toolbar:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

@media (min-width: 768px) {

  .fc .fc-toolbar.fc-header-toolbar {
    flex-direction: row !important;
  }

  .fc .fc-toolbar.fc-header-toolbar > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0 !important;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))) !important;
    margin-bottom: calc(0px * var(--tw-space-y-reverse)) !important;
  }
}

.fc .fc-toolbar-title {
  flex-shrink: 0 !important;
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
  font-weight: 600 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-toolbar-title:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-today-button {
  margin-left: 1rem !important;
  margin-right: 0px !important;
  cursor: pointer !important;
  border-radius: 0.5rem !important;
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
  opacity: 1 !important;
}

.fc .fc-today-button:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(29 78 216 / var(--tw-text-opacity)) !important;
}

.fc .fc-today-button:focus {
  z-index: 10 !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity)) !important;
}

.fc .fc-today-button:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(75 85 99 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.fc .fc-today-button:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-today-button:focus:is(.dark *) {
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary {
  border-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(29 78 216 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:focus {
  z-index: 10 !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(75 85 99 / var(--tw-border-opacity)) !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:focus:is(.dark *) {
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity)) !important;
}

.fc .fc-button-group .fc-button-primary:first-child {
  border-top-left-radius: 0.5rem !important;
  border-bottom-left-radius: 0.5rem !important;
}

.fc .fc-button-group .fc-button-primary:last-child {
  border-top-right-radius: 0.5rem !important;
  border-bottom-right-radius: 0.5rem !important;
}

.fc .fc-button-group .fc-prev-button,
.fc .fc-button-group .fc-next-button {
  display: inline-flex !important;
  max-width: 3rem !important;
  cursor: pointer !important;
  justify-content: center !important;
  border-radius: 0.25rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-prev-button:hover,
.fc .fc-button-group .fc-next-button:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-button-group .fc-prev-button:focus,
.fc .fc-button-group .fc-next-button:focus {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity)) !important;
}

.fc .fc-button-group .fc-prev-button:is(.dark *),
.fc .fc-button-group .fc-next-button:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

.fc .fc-scrollgrid {
  border-left-width: 0px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}

.fc .fc-scrollgrid:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 41 55 / var(--tw-border-opacity)) !important;
}

.fc .fc-daygrid-day-frame {
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}

.fc .fc-daygrid-day-frame:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 41 55 / var(--tw-border-opacity)) !important;
}

.fc .fc-col-header-cell-cushion {
  padding-top: 0.75rem !important;
  padding-bottom: 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  font-weight: 600 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc-theme-standard th {
  border-width: 0px !important;
  border-bottom-width: 1px !important;
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}

.fc-theme-standard th:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 41 55 / var(--tw-border-opacity)) !important;
}

.fc-direction-ltr .fc-daygrid-event.fc-event-end {
  margin-right: 0.5rem !important;
}

.fc-direction-ltr .fc-daygrid-event.fc-event-start {
  margin-left: 0.5rem !important;
}

.fc .fc-event .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(29 78 216 / var(--tw-text-opacity)) !important;
}

.fc .fc-event .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(147 197 253 / var(--tw-text-opacity)) !important;
}

.fc .fc-event .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(246 245 255 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(108 43 217 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(237 235 254 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(74 29 150 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(202 191 253 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(85 33 181 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(108 43 217 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-purple .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(202 191 253 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(240 245 255 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(81 69 205 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(229 237 255 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(54 47 120 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(180 198 252 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(66 56 157 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(81 69 205 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-indigo .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(180 198 252 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 242 248 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(191 18 93 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(252 232 243 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(117 26 61 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(248 180 217 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(153 21 75 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(191 18 93 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-pink .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(248 180 217 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(237 250 250 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(3 102 114 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(1 68 81 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(126 220 226 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(5 80 92 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(3 102 114 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-teal .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(126 220 226 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 250 247 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(4 108 78 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(222 247 236 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(1 71 55 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(132 225 188 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(3 84 63 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(4 108 78 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-green .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(132 225 188 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 253 234 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(142 75 16 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 246 178 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(99 49 18 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(250 202 21 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(114 59 19 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(142 75 16 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-yellow .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(250 202 21 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 248 241 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(180 52 3 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(254 236 220 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(119 29 29 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(253 186 140 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(138 44 13 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(180 52 3 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-orange .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 186 140 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 242 242 / var(--tw-bg-opacity)) !important;
  padding: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(200 30 30 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(119 29 29 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(248 180 180 / var(--tw-text-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(155 28 28 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main-frame:before {
  content: "";
  margin-right: 0.375rem !important;
  height: 0.5rem !important;
  width: 0.5rem !important;
  border-radius: 9999px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event.fc-event-red .fc-event-main-frame:is(.dark *):before {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(248 180 180 / var(--tw-bg-opacity)) !important;
}

.fc .fc-event {
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  background-color: transparent !important;
}

.fc .fc-h-event .fc-event-main-frame {
  font-size: 0.75rem !important;
  line-height: 1rem !important;
  font-weight: 600 !important;
}

.fc .fc-daygrid-day-frame:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
}

.fc .fc-daygrid-day-frame {
  cursor: pointer !important;
}

.fc .fc-daygrid-day-frame:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
}

.fc .fc-daygrid-day-frame:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

.fc .fc-addEventButton-button {
  margin-left: 0px !important;
  margin-right: 0px !important;
  display: inline-flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 0.5rem !important;
  border-width: 0px !important;
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
  padding-left: 0.75rem !important;
  padding-right: 0.75rem !important;
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
  font-weight: 500 !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-addEventButton-button:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity)) !important;
}

.fc .fc-addEventButton-button:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity)) !important;
}

.fc .fc-addEventButton-button:active {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity)) !important;
}

.fc .fc-addEventButton-button:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity)) !important;
}

.fc .fc-addEventButton-button:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}

.fc .fc-addEventButton-button:focus:is(.dark *) {
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity)) !important;
}

.fc .fc-addEventButton-button:active:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}

@media (min-width: 640px) {

  .fc .fc-addEventButton-button {
    margin-left: 1rem !important;
    width: auto !important;
  }
}

.fc .fc-toolbar-chunk {
  display: flex !important;
  width: 100% !important;
  align-items: center !important;
  justify-content: center !important;
}

@media (min-width: 640px) {

  .fc .fc-toolbar-chunk {
    width: auto !important;
    justify-content: flex-start !important;
  }
}

.fc .fc-toolbar-chunk:last-child {
  flex-direction: column !important;
}

.fc .fc-toolbar-chunk:last-child > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0 !important;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse))) !important;
  margin-bottom: calc(1rem * var(--tw-space-y-reverse)) !important;
}

@media (min-width: 640px) {

  .fc .fc-toolbar-chunk:last-child {
    flex-direction: row !important;
  }

  .fc .fc-toolbar-chunk:last-child > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0 !important;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse))) !important;
    margin-bottom: calc(0px * var(--tw-space-y-reverse)) !important;
  }
}

.fc .fc-toolbar-chunk > .fc-button-group {
  width: 100% !important;
}

@media (min-width: 640px) {

  .fc .fc-toolbar-chunk > .fc-button-group {
    width: auto !important;
  }
}

.fc-theme-standard td,
.fc-theme-standard th {
  --tw-border-opacity: 1 !important;
  border-color: rgb(229 231 235 / var(--tw-border-opacity)) !important;
}

.fc-theme-standard td:is(.dark *),
.fc-theme-standard th:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(31 41 55 / var(--tw-border-opacity)) !important;
}

.fc .fc-daygrid-day-number {
  font-size: 1rem !important;
  line-height: 1.5rem !important;
  font-weight: 500 !important;
}

.fc .fc-daygrid-day-number,
.fc .fc-col-header-cell-cushion {
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-daygrid-day-number:is(.dark *),
.fc .fc-col-header-cell-cushion:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-daygrid-day-top {
  display: flex !important;
  justify-content: center !important;
}

.fc .fc-daygrid-day.fc-day-today {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
}

.fc .fc-daygrid-day.fc-day-today:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
}

.fc .fc-daygrid-event-harness,
.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs {
  margin-bottom: 0.5rem !important;
}

.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs {
  margin-left: 0px !important;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.fc .fc-daygrid-event-harness.fc-daygrid-event-harness-abs .fc-daygrid-event.fc-event-start {
  margin-left: 0px !important;
}

.fc-event-main-frame {
  display: flex !important;
  align-items: center !important;
}

.fc .fc-timegrid-slot-label-frame.fc-scrollgrid-shrink-frame,
.fc .fc-timegrid-axis-frame.fc-scrollgrid-shrink-frame.fc-timegrid-axis-frame-liquid,
.fc .fc-list-day-side-text {
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
}

.fc .fc-timegrid-slot-label-frame.fc-scrollgrid-shrink-frame:is(.dark *),
.fc .fc-timegrid-axis-frame.fc-scrollgrid-shrink-frame.fc-timegrid-axis-frame-liquid:is(.dark *),
.fc .fc-list-day-side-text:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.fc .fc-list-day-cushion.fc-cell-shaded {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}

.fc .fc-list-day-cushion.fc-cell-shaded:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity)) !important;
}

.fc.fc-theme-standard .fc-list {
  border-color: transparent !important;
}

.fc .fc-list-day-text {
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-list-day-text:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-list .fc-event {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(17 24 39 / var(--tw-text-opacity)) !important;
}

.fc .fc-list .fc-event:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity)) !important;
}

.fc .fc-list .fc-event:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity)) !important;
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.fc .fc-list .fc-event:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
}

.fc-timegrid-event-harness-inset .fc-timegrid-event,
.fc-timegrid-event.fc-event-mirror,
.fc-timegrid-more-link {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.fc .fc-daygrid-dot-event .fc-event-time,
.fc .fc-daygrid-dot-event .fc-event-title {
  --tw-text-opacity: 1 !important;
  color: rgb(107 114 128 / var(--tw-text-opacity)) !important;
}

.fc .fc-daygrid-dot-event .fc-event-time:is(.dark *),
.fc .fc-daygrid-dot-event .fc-event-title:is(.dark *) {
  --tw-text-opacity: 1 !important;
  color: rgb(156 163 175 / var(--tw-text-opacity)) !important;
}

.fc .fc-timegrid-divider {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

/* feed styles */

@media (min-width: 768px) {
  .feed-container {
    height: calc(100vh - 4rem);
  }
}

.fc .fc-list-event:hover td:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
}

.fc-day-today {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity)) !important;
}

.fc-day-today:is(.dark *) {
  background-color: rgb(31 41 55 / 0.4) !important;
}

.tiptap p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  height: 0px;
  cursor: none;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.tiptap p.is-editor-empty:first-child:is(.dark *)::before {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:format-invert:is(.dark *) {
  --tw-format-body: var(--tw-format-invert-body);
  --tw-format-headings: var(--tw-format-invert-headings);
  --tw-format-lead: var(--tw-format-invert-lead);
  --tw-format-links: var(--tw-format-invert-links);
  --tw-format-bold: var(--tw-format-invert-bold);
  --tw-format-counters: var(--tw-format-invert-counters);
  --tw-format-bullets: var(--tw-format-invert-bullets);
  --tw-format-hr: var(--tw-format-invert-hr);
  --tw-format-quotes: var(--tw-format-invert-quotes);
  --tw-format-quote-borders: var(--tw-format-invert-quote-borders);
  --tw-format-captions: var(--tw-format-invert-captions);
  --tw-format-code: var(--tw-format-invert-code);
  --tw-format-code-bg: var(--tw-format-invert-code-bg);
  --tw-format-pre-code: var(--tw-format-invert-pre-code);
  --tw-format-pre-bg: var(--tw-format-invert-pre-bg);
  --tw-format-th-borders: var(--tw-format-invert-th-borders);
  --tw-format-td-borders: var(--tw-format-invert-td-borders);
  --tw-format-th-bg: var(--tw-format-invert-th-bg);
}

.placeholder\:text-gray-500::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.placeholder\:text-gray-500::placeholder {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.placeholder\:text-gray-900::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.placeholder\:text-gray-900::placeholder {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:start-\[2px\]::after {
  content: var(--tw-content);
  inset-inline-start: 2px;
}

.after\:top-0\.5::after {
  content: var(--tw-content);
  top: 0.125rem;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:h-4::after {
  content: var(--tw-content);
  height: 1rem;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-4::after {
  content: var(--tw-content);
  width: 1rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.hover\:cursor-pointer:hover {
  cursor: pointer;
}

.hover\:border-b-2:hover {
  border-bottom-width: 2px;
}

.hover\:border-gray-200:hover {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.hover\:border-primary-600:hover {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.hover\:border-primary-700:hover {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.hover\:border-primary-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity));
}

.hover\:border-red-800:hover {
  --tw-border-opacity: 1;
  border-color: rgb(155 28 28 / var(--tw-border-opacity));
}

.hover\:\!bg-primary-800:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity)) !important;
}

.hover\:bg-\[\#4285F4\]\/90:hover {
  background-color: rgb(66 133 244 / 0.9);
}

.hover\:bg-blue-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 66 159 / var(--tw-bg-opacity));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-gray-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(5 122 85 / var(--tw-bg-opacity));
}

.hover\:bg-green-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(4 108 78 / var(--tw-bg-opacity));
}

.hover\:bg-indigo-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(104 117 245 / var(--tw-bg-opacity));
}

.hover\:bg-orange-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(180 52 3 / var(--tw-bg-opacity));
}

.hover\:bg-pink-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(231 70 148 / var(--tw-bg-opacity));
}

.hover\:bg-primary-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity));
}

.hover\:bg-primary-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity));
}

.hover\:bg-primary-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.hover\:bg-primary-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.hover\:bg-primary-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.hover\:bg-purple-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(237 235 254 / var(--tw-bg-opacity));
}

.hover\:bg-purple-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(144 97 249 / var(--tw-bg-opacity));
}

.hover\:bg-red-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 232 232 / var(--tw-bg-opacity));
}

.hover\:bg-red-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(253 242 242 / var(--tw-bg-opacity));
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}

.hover\:bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}

.hover\:bg-red-800:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(155 28 28 / var(--tw-bg-opacity));
}

.hover\:bg-teal-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(213 245 246 / var(--tw-bg-opacity));
}

.hover\:bg-teal-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(6 148 162 / var(--tw-bg-opacity));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:bg-white\/50:hover {
  background-color: rgb(255 255 255 / 0.5);
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(28 100 242 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.hover\:text-primary-700:hover {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.hover\:text-primary-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.hover\:text-primary-900:hover {
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity));
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-yellow-400:hover {
  --tw-text-opacity: 1;
  color: rgb(227 160 8 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:no-underline:hover {
  text-decoration-line: none;
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:border-gray-200:focus {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.focus\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.focus\:border-primary-600:focus {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.focus\:text-primary-700:focus {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:\!ring-primary-300:focus {
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity)) !important;
}

.focus\:ring-\[\#4285F4\]\/50:focus {
  --tw-ring-color: rgb(66 133 244 / 0.5);
}

.focus\:ring-blue-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(164 202 254 / var(--tw-ring-opacity));
}

.focus\:ring-gray-100:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 244 246 / var(--tw-ring-opacity));
}

.focus\:ring-gray-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity));
}

.focus\:ring-gray-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity));
}

.focus\:ring-gray-50:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 250 251 / var(--tw-ring-opacity));
}

.focus\:ring-green-100:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(222 247 236 / var(--tw-ring-opacity));
}

.focus\:ring-primary-100:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(219 234 254 / var(--tw-ring-opacity));
}

.focus\:ring-primary-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity));
}

.focus\:ring-primary-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.focus\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.focus\:ring-primary-600:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.focus\:ring-primary-700:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity));
}

.focus\:ring-red-200:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 213 213 / var(--tw-ring-opacity));
}

.focus\:ring-red-300:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 180 180 / var(--tw-ring-opacity));
}

.group:hover .group-hover\:bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-primary-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-purple-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 215 254 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-teal-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(175 236 239 / var(--tw-bg-opacity));
}

.group:hover .group-hover\:bg-white\/50 {
  background-color: rgb(255 255 255 / 0.5);
}

.group:hover .group-hover\:text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.group:hover .group-hover\:text-primary-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.group:hover .group-hover\:underline {
  text-decoration-line: underline;
}

.group\/image:hover .group-hover\/image\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:focus .group-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.group:focus .group-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.group:focus .group-focus\:ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}

.peer:checked ~ .peer-checked\:border-primary-700 {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.peer:checked ~ .peer-checked\:bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:bg-primary-700 {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.peer:checked ~ .peer-checked\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:placeholder-shown ~ .peer-placeholder-shown\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:start-0 {
  inset-inline-start: 0px;
}

.peer:focus ~ .peer-focus\:-translate-y-6 {
  --tw-translate-y: -1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:scale-75 {
  --tw-scale-x: .75;
  --tw-scale-y: .75;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .peer-focus\:text-primary-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.peer:focus ~ .peer-focus\:outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-primary-300 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-green-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(132 225 188 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-indigo-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(180 198 252 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-orange-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 186 140 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-pink-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 180 217 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-primary-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-purple-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(202 191 253 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-red-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 180 180 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-teal-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(126 220 226 / var(--tw-ring-opacity));
}

.data-\[color-selected\=true\]\:ring-yellow-300[data-color-selected="true"] {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 202 21 / var(--tw-ring-opacity));
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:flex:is(.dark *) {
  display: flex;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:divide-gray-600:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-divide-opacity));
}

.dark\:divide-gray-700:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-divide-opacity));
}

.dark\:divide-gray-800:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-divide-opacity));
}

.dark\:\!border-gray-700:is(.dark *) {
  --tw-border-opacity: 1 !important;
  border-color: rgb(55 65 81 / var(--tw-border-opacity)) !important;
}

.dark\:border-blue-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(63 131 248 / var(--tw-border-opacity));
}

.dark\:border-gray-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:border-gray-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity));
}

.dark\:border-gray-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity));
}

.dark\:border-green-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(3 84 63 / var(--tw-border-opacity));
}

.dark\:border-primary-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.dark\:border-primary-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.dark\:border-red-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(240 82 82 / var(--tw-border-opacity));
}

.dark\:border-transparent:is(.dark *) {
  border-color: transparent;
}

.dark\:border-s-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-inline-start-color: rgb(55 65 81 / var(--tw-border-opacity));
}

.dark\:\!bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity)) !important;
}

.dark\:\!bg-primary-600:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity)) !important;
}

.dark\:\!bg-primary-700:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}

.dark\:bg-blue-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(28 100 242 / var(--tw-bg-opacity));
}

.dark\:bg-gray-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.dark\:bg-gray-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.dark\:bg-gray-800\/30:is(.dark *) {
  background-color: rgb(31 41 55 / 0.3);
}

.dark\:bg-gray-800\/50:is(.dark *) {
  background-color: rgb(31 41 55 / 0.5);
}

.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.dark\:bg-gray-900\/80:is(.dark *) {
  background-color: rgb(17 24 39 / 0.8);
}

.dark\:bg-green-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(14 159 110 / var(--tw-bg-opacity));
}

.dark\:bg-green-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(1 71 55 / var(--tw-bg-opacity));
}

.dark\:bg-indigo-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(54 47 120 / var(--tw-bg-opacity));
}

.dark\:bg-pink-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(117 26 61 / var(--tw-bg-opacity));
}

.dark\:bg-primary-200:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity));
}

.dark\:bg-primary-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.dark\:bg-primary-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.dark\:bg-primary-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.dark\:bg-primary-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}

.dark\:bg-purple-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(85 33 181 / var(--tw-bg-opacity));
}

.dark\:bg-purple-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(74 29 150 / var(--tw-bg-opacity));
}

.dark\:bg-red-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(240 82 82 / var(--tw-bg-opacity));
}

.dark\:bg-red-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}

.dark\:bg-red-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(119 29 29 / var(--tw-bg-opacity));
}

.dark\:bg-teal-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(5 80 92 / var(--tw-bg-opacity));
}

.dark\:bg-teal-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(1 68 81 / var(--tw-bg-opacity));
}

.dark\:bg-yellow-900:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(99 49 18 / var(--tw-bg-opacity));
}

.dark\:bg-opacity-80:is(.dark *) {
  --tw-bg-opacity: 0.8;
}

.dark\:bg-\[url\(\'https\:\/\/flowbite\.s3\.amazonaws\.com\/blocks\/e-commerce\/gaming-image\.jpg\'\)\]:is(.dark *) {
  background-image: url('https://flowbite.s3.amazonaws.com/blocks/e-commerce/gaming-image.jpg');
}

.dark\:fill-gray-400:is(.dark *) {
  fill: #9CA3AF;
}

.dark\:fill-gray-500:is(.dark *) {
  fill: #6B7280;
}

.dark\:fill-white:is(.dark *) {
  fill: #ffffff;
}

.dark\:text-blue-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}

.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:text-gray-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity));
}

.dark\:text-gray-700:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.dark\:text-gray-800:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(132 225 188 / var(--tw-text-opacity));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(49 196 141 / var(--tw-text-opacity));
}

.dark\:text-green-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(14 159 110 / var(--tw-text-opacity));
}

.dark\:text-indigo-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(180 198 252 / var(--tw-text-opacity));
}

.dark\:text-indigo-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(141 162 251 / var(--tw-text-opacity));
}

.dark\:text-orange-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 186 140 / var(--tw-text-opacity));
}

.dark\:text-pink-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(241 126 184 / var(--tw-text-opacity));
}

.dark\:text-primary-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}

.dark\:text-primary-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.dark\:text-primary-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.dark\:text-primary-600:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.dark\:text-primary-800:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity));
}

.dark\:text-purple-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(202 191 253 / var(--tw-text-opacity));
}

.dark\:text-purple-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(172 148 250 / var(--tw-text-opacity));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 180 180 / var(--tw-text-opacity));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 128 128 / var(--tw-text-opacity));
}

.dark\:text-red-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(240 82 82 / var(--tw-text-opacity));
}

.dark\:text-teal-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(126 220 226 / var(--tw-text-opacity));
}

.dark\:text-teal-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(22 189 202 / var(--tw-text-opacity));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:text-yellow-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 202 21 / var(--tw-text-opacity));
}

.dark\:text-yellow-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(227 160 8 / var(--tw-text-opacity));
}

.dark\:placeholder-gray-400:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity));
}

.dark\:ring-gray-800:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}

.dark\:ring-offset-gray-700:is(.dark *) {
  --tw-ring-offset-color: #374151;
}

.dark\:ring-offset-gray-800:is(.dark *) {
  --tw-ring-offset-color: #1F2937;
}

.dark\:invert:is(.dark *) {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.dark\:placeholder\:text-gray-400:is(.dark *)::-moz-placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:placeholder\:text-gray-400:is(.dark *)::placeholder {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.dark\:hover\:border-gray-500:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity));
}

.dark\:hover\:border-gray-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity));
}

.dark\:hover\:border-primary-500:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.dark\:hover\:border-primary-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.dark\:hover\:border-primary-700:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity));
}

.dark\:hover\:\!bg-primary-700:hover:is(.dark *) {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity)) !important;
}

.dark\:hover\:bg-blue-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(26 86 219 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-500:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-green-400:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(49 196 141 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-500:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-primary-900:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-purple-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(85 33 181 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-red-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(224 36 36 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-red-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(200 30 30 / var(--tw-bg-opacity));
}

.dark\:hover\:bg-teal-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(5 80 92 / var(--tw-bg-opacity));
}

.dark\:hover\:text-blue-500:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(63 131 248 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-100:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-500:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-600:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity));
}

.dark\:hover\:text-primary-700:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity));
}

.dark\:hover\:text-red-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(249 128 128 / var(--tw-text-opacity));
}

.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:hover\:text-yellow-400:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(227 160 8 / var(--tw-text-opacity));
}

.dark\:hover\:underline:hover:is(.dark *) {
  text-decoration-line: underline;
}

.dark\:focus\:border-primary-500:focus:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.dark\:focus\:text-white:focus:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.dark\:focus\:ring-\[\#4285F4\]\/55:focus:is(.dark *) {
  --tw-ring-color: rgb(66 133 244 / 0.55);
}

.dark\:focus\:ring-gray-600:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(75 85 99 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-700:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(55 65 81 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-gray-800:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-green-700:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(4 108 78 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-primary-500:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-primary-600:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(37 99 235 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-primary-700:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(29 78 216 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-primary-800:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-primary-900:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 58 138 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-800:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(155 28 28 / var(--tw-ring-opacity));
}

.dark\:focus\:ring-red-900:focus:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(119 29 29 / var(--tw-ring-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-gray-800\/60:is(.dark *) {
  background-color: rgb(31 41 55 / 0.6);
}

.group:hover .dark\:group-hover\:bg-primary-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-purple-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(108 43 217 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:bg-teal-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(3 102 114 / var(--tw-bg-opacity));
}

.group:hover .dark\:group-hover\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.group:hover .dark\:group-hover\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.group:focus .dark\:group-focus\:ring-gray-800\/70:is(.dark *) {
  --tw-ring-color: rgb(31 41 55 / 0.7);
}

.peer:checked ~ .dark\:peer-checked\:border-primary-500:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity));
}

.peer:checked ~ .dark\:peer-checked\:border-primary-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity));
}

.peer:checked ~ .dark\:peer-checked\:bg-primary-500:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}

.peer:checked ~ .dark\:peer-checked\:bg-primary-600:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity));
}

.peer:focus ~ .peer-focus\:dark\:text-primary-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}

.peer:focus ~ .dark\:peer-focus\:ring-primary-800:is(.dark *) {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity));
}

@media (min-width: 640px) {

  .sm\:relative {
    position: relative;
  }

  .sm\:end-0 {
    inset-inline-end: 0px;
  }

  .sm\:top-0 {
    top: 0px;
  }

  .sm\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .sm\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .sm\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .sm\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .sm\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .sm\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .sm\:-ms-1 {
    margin-inline-start: -0.25rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:mb-1\.5 {
    margin-bottom: 0.375rem;
  }

  .sm\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .sm\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .sm\:mb-4 {
    margin-bottom: 1rem;
  }

  .sm\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .sm\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .sm\:me-0 {
    margin-inline-end: 0px;
  }

  .sm\:ml-auto {
    margin-left: auto;
  }

  .sm\:mr-2 {
    margin-right: 0.5rem;
  }

  .sm\:mr-4 {
    margin-right: 1rem;
  }

  .sm\:ms-0 {
    margin-inline-start: 0px;
  }

  .sm\:ms-4 {
    margin-inline-start: 1rem;
  }

  .sm\:ms-7 {
    margin-inline-start: 1.75rem;
  }

  .sm\:ms-auto {
    margin-inline-start: auto;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-5 {
    margin-top: 1.25rem;
  }

  .sm\:mt-6 {
    margin-top: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:inline-flex {
    display: inline-flex;
  }

  .sm\:grid {
    display: grid;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-16 {
    height: 4rem;
  }

  .sm\:h-24 {
    height: 6rem;
  }

  .sm\:h-3\.5 {
    height: 0.875rem;
  }

  .sm\:h-36 {
    height: 9rem;
  }

  .sm\:h-40 {
    height: 10rem;
  }

  .sm\:h-8 {
    height: 2rem;
  }

  .sm\:h-\[calc\(100vh-15\.4rem\)\] {
    height: calc(100vh - 15.4rem);
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-16 {
    width: 4rem;
  }

  .sm\:w-24 {
    width: 6rem;
  }

  .sm\:w-3\.5 {
    width: 0.875rem;
  }

  .sm\:w-36 {
    width: 9rem;
  }

  .sm\:w-40 {
    width: 10rem;
  }

  .sm\:w-8 {
    width: 2rem;
  }

  .sm\:w-80 {
    width: 20rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-full {
    width: 100%;
  }

  .sm\:min-w-64 {
    min-width: 16rem;
  }

  .sm\:min-w-72 {
    min-width: 18rem;
  }

  .sm\:max-w-\[15rem\] {
    max-width: 15rem;
  }

  .sm\:max-w-xl {
    max-width: 36rem;
  }

  .sm\:flex-none {
    flex: none;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-24 {
    gap: 6rem;
  }

  .sm\:gap-4 {
    gap: 1rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:space-x-24 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(6rem * var(--tw-space-x-reverse));
    margin-left: calc(6rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-5 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.25rem * var(--tw-space-x-reverse));
    margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .sm\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .sm\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .sm\:divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  .sm\:divide-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-y-reverse: 0;
    border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width: calc(0px * var(--tw-divide-y-reverse));
  }

  .sm\:rounded-lg {
    border-radius: 0.5rem;
  }

  .sm\:border-0 {
    border-width: 0px;
  }

  .sm\:border-b-0 {
    border-bottom-width: 0px;
  }

  .sm\:border-e {
    border-inline-end-width: 1px;
  }

  .sm\:border-s {
    border-inline-start-width: 1px;
  }

  .sm\:border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity));
  }

  .sm\:border-transparent {
    border-color: transparent;
  }

  .sm\:p-5 {
    padding: 1.25rem;
  }

  .sm\:p-6 {
    padding: 1.5rem;
  }

  .sm\:p-8 {
    padding: 2rem;
  }

  .sm\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .sm\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .sm\:py-28 {
    padding-top: 7rem;
    padding-bottom: 7rem;
  }

  .sm\:py-3\.5 {
    padding-top: 0.875rem;
    padding-bottom: 0.875rem;
  }

  .sm\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .sm\:py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pb-0 {
    padding-bottom: 0px;
  }

  .sm\:pb-2\.5 {
    padding-bottom: 0.625rem;
  }

  .sm\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .sm\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .sm\:pe-3 {
    padding-inline-end: 0.75rem;
  }

  .sm\:pe-4 {
    padding-inline-end: 1rem;
  }

  .sm\:pe-6 {
    padding-inline-end: 1.5rem;
  }

  .sm\:pl-2 {
    padding-left: 0.5rem;
  }

  .sm\:pr-2 {
    padding-right: 0.5rem;
  }

  .sm\:ps-2 {
    padding-inline-start: 0.5rem;
  }

  .sm\:ps-4 {
    padding-inline-start: 1rem;
  }

  .sm\:ps-5 {
    padding-inline-start: 1.25rem;
  }

  .sm\:ps-6 {
    padding-inline-start: 1.5rem;
  }

  .sm\:pt-0 {
    padding-top: 0px;
  }

  .sm\:pt-16 {
    padding-top: 4rem;
  }

  .sm\:pt-5 {
    padding-top: 1.25rem;
  }

  .sm\:pt-6 {
    padding-top: 1.5rem;
  }

  .sm\:text-center {
    text-align: center;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:leading-none {
    line-height: 1;
  }

  .sm\:dark\:border-gray-700:is(.dark *) {
    --tw-border-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-border-opacity));
  }
}

@media (min-width: 768px) {

  .md\:sticky {
    position: sticky;
  }

  .md\:inset-0 {
    inset: 0px;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .md\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .md\:my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .md\:my-5 {
    margin-top: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .md\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .md\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mb-4 {
    margin-bottom: 1rem;
  }

  .md\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:me-0 {
    margin-inline-end: 0px;
  }

  .md\:ml-2 {
    margin-left: 0.5rem;
  }

  .md\:mr-4 {
    margin-right: 1rem;
  }

  .md\:ms-2 {
    margin-inline-start: 0.5rem;
  }

  .md\:ms-auto {
    margin-inline-start: auto;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:mt-8 {
    margin-top: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:flex {
    display: flex;
  }

  .md\:inline-flex {
    display: inline-flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-\[40px\] {
    height: 40px;
  }

  .md\:h-full {
    height: 100%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-64 {
    width: 16rem;
  }

  .md\:w-\[185px\] {
    width: 185px;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:max-w-lg {
    max-width: 32rem;
  }

  .md\:max-w-md {
    max-width: 28rem;
  }

  .md\:max-w-sm {
    max-width: 24rem;
  }

  .md\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-24 {
    gap: 6rem;
  }

  .md\:gap-6 {
    gap: 1.5rem;
  }

  .md\:gap-x-8 {
    -moz-column-gap: 2rem;
         column-gap: 2rem;
  }

  .md\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .md\:rounded-l-lg {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
  }

  .md\:rounded-r-lg {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
  }

  .md\:rounded-bl-none {
    border-bottom-left-radius: 0px;
  }

  .md\:rounded-tr-none {
    border-top-right-radius: 0px;
  }

  .md\:border-x-0 {
    border-left-width: 0px;
    border-right-width: 0px;
  }

  .md\:border-b {
    border-bottom-width: 1px;
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-t {
    border-top-width: 1px;
  }

  .md\:p-5 {
    padding: 1.25rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:p-8 {
    padding: 2rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pb-5 {
    padding-bottom: 1.25rem;
  }

  .md\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .md\:pe-0 {
    padding-inline-end: 0px;
  }

  .md\:pt-5 {
    padding-top: 1.25rem;
  }

  .md\:pt-6 {
    padding-top: 1.5rem;
  }

  .md\:text-start {
    text-align: start;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {

  .lg\:fixed {
    position: fixed;
  }

  .lg\:bottom-0 {
    bottom: 0px;
  }

  .lg\:z-0 {
    z-index: 0;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-3 {
    order: 3;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .lg\:my-8 {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .lg\:mb-0 {
    margin-bottom: 0px;
  }

  .lg\:mb-10 {
    margin-bottom: 2.5rem;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:mb-4 {
    margin-bottom: 1rem;
  }

  .lg\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .lg\:ml-16 {
    margin-left: 4rem;
  }

  .lg\:mr-4 {
    margin-right: 1rem;
  }

  .lg\:ms-64 {
    margin-inline-start: 16rem;
  }

  .lg\:mt-0 {
    margin-top: 0px;
  }

  .lg\:mt-16 {
    margin-top: 4rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:inline-flex {
    display: inline-flex;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-40 {
    height: 10rem;
  }

  .lg\:h-6 {
    height: 1.5rem;
  }

  .lg\:h-\[calc\(100vh-8rem\)\] {
    height: calc(100vh - 8rem);
  }

  .lg\:max-h-\[60rem\] {
    max-height: 60rem;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-6 {
    width: 1.5rem;
  }

  .lg\:w-60 {
    width: 15rem;
  }

  .lg\:w-96 {
    width: 24rem;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-full {
    width: 100%;
  }

  .lg\:max-w-7xl {
    max-width: 80rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:translate-x-0 {
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\:grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:items-center {
    align-items: center;
  }

  .lg\:justify-end {
    justify-content: flex-end;
  }

  .lg\:justify-between {
    justify-content: space-between;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-16 {
    gap: 4rem;
  }

  .lg\:gap-20 {
    gap: 5rem;
  }

  .lg\:gap-24 {
    gap: 6rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:gap-x-4 {
    -moz-column-gap: 1rem;
         column-gap: 1rem;
  }

  .lg\:gap-y-0 {
    row-gap: 0px;
  }

  .lg\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(2rem * var(--tw-space-x-reverse));
    margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:space-y-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
  }

  .lg\:rounded-lg {
    border-radius: 0.5rem;
  }

  .lg\:border-0 {
    border-width: 0px;
  }

  .lg\:border-b {
    border-bottom-width: 1px;
  }

  .lg\:border-b-0 {
    border-bottom-width: 0px;
  }

  .lg\:border-gray-200 {
    --tw-border-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-border-opacity));
  }

  .lg\:bg-transparent {
    background-color: transparent;
  }

  .lg\:p-0 {
    padding: 0px;
  }

  .lg\:p-10 {
    padding: 2.5rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .lg\:py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .lg\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .lg\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .lg\:pb-0 {
    padding-bottom: 0px;
  }

  .lg\:pb-6 {
    padding-bottom: 1.5rem;
  }

  .lg\:pb-8 {
    padding-bottom: 2rem;
  }

  .lg\:pl-2 {
    padding-left: 0.5rem;
  }

  .lg\:ps-8 {
    padding-inline-start: 2rem;
  }

  .lg\:pt-0 {
    padding-top: 0px;
  }

  .lg\:pt-16 {
    padding-top: 4rem;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .lg\:text-primary-700 {
    --tw-text-opacity: 1;
    color: rgb(29 78 216 / var(--tw-text-opacity));
  }

  .lg\:transition-width {
    transition-property: width;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
  }

  .lg\:hover\:bg-transparent:hover {
    background-color: transparent;
  }

  .lg\:hover\:text-primary-700:hover {
    --tw-text-opacity: 1;
    color: rgb(29 78 216 / var(--tw-text-opacity));
  }

  .lg\:dark\:hover\:bg-transparent:hover:is(.dark *) {
    background-color: transparent;
  }

  .lg\:dark\:hover\:text-white:hover:is(.dark *) {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity));
  }
}

@media (min-width: 1280px) {

  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:mb-0 {
    margin-bottom: 0px;
  }

  .xl\:mr-0 {
    margin-right: 0px;
  }

  .xl\:mt-6 {
    margin-top: 1.5rem;
  }

  .xl\:block {
    display: block;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:grid {
    display: grid;
  }

  .xl\:w-2\/3 {
    width: 66.666667%;
  }

  .xl\:max-w-4xl {
    max-width: 56rem;
  }

  .xl\:max-w-lg {
    max-width: 32rem;
  }

  .xl\:max-w-screen-md {
    max-width: 768px;
  }

  .xl\:\!translate-x-0 {
    --tw-translate-x: 0px !important;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important;
  }

  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .xl\:items-center {
    align-items: center;
  }

  .xl\:justify-between {
    justify-content: space-between;
  }

  .xl\:gap-10 {
    gap: 2.5rem;
  }

  .xl\:gap-16 {
    gap: 4rem;
  }

  .xl\:gap-4 {
    gap: 1rem;
  }

  .xl\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-x-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0.75rem * var(--tw-space-x-reverse));
    margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .xl\:space-y-3 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
  }

  .xl\:border-b-0 {
    border-bottom-width: 0px;
  }

  .xl\:border-e {
    border-inline-end-width: 1px;
  }

  .xl\:p-8 {
    padding: 2rem;
  }

  .xl\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .xl\:pb-0 {
    padding-bottom: 0px;
  }

  .xl\:ps-4 {
    padding-inline-start: 1rem;
  }

  .xl\:pt-0 {
    padding-top: 0px;
  }

  .xl\:text-center {
    text-align: center;
  }
}

@media (min-width: 1536px) {

  .\32xl\:absolute {
    position: absolute;
  }

  .\32xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .\32xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .\32xl\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .\32xl\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .\32xl\:col-span-5 {
    grid-column: span 5 / span 5;
  }

  .\32xl\:col-span-7 {
    grid-column: span 7 / span 7;
  }

  .\32xl\:col-span-8 {
    grid-column: span 8 / span 8;
  }

  .\32xl\:col-span-9 {
    grid-column: span 9 / span 9;
  }

  .\32xl\:mt-8 {
    margin-top: 2rem;
  }

  .\32xl\:flex {
    display: flex;
  }

  .\32xl\:max-h-fit {
    max-height: -moz-fit-content;
    max-height: fit-content;
  }

  .\32xl\:max-w-screen-lg {
    max-width: 1024px;
  }

  .\32xl\:grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .\32xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .\32xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .\32xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .\32xl\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .\32xl\:gap-16 {
    gap: 4rem;
  }

  .\32xl\:gap-24 {
    gap: 6rem;
  }

  .\32xl\:gap-60 {
    gap: 15rem;
  }

  .\32xl\:space-x-24 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(6rem * var(--tw-space-x-reverse));
    margin-left: calc(6rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .\32xl\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1rem * var(--tw-space-x-reverse));
    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .\32xl\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .\32xl\:divide-x > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 0;
    border-right-width: calc(1px * var(--tw-divide-x-reverse));
    border-left-width: calc(1px * calc(1 - var(--tw-divide-x-reverse)));
  }

  .\32xl\:divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(229 231 235 / var(--tw-divide-opacity));
  }

  .\32xl\:p-6 {
    padding: 1.5rem;
  }

  .\32xl\:ps-8 {
    padding-inline-start: 2rem;
  }
}

@media (min-width: 1928px) {

  .min-\[1928px\]\:flex {
    display: flex;
  }
}

.rtl\:translate-x-1\/2:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:-rotate-90:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:rotate-180:where([dir="rtl"], [dir="rtl"] *) {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *) {
  text-align: right;
}

.peer:checked ~ .rtl\:peer-checked\:after\:-translate-x-full:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .rtl\:peer-checked\:after\:translate-x-\[-100\%\]:where([dir="rtl"], [dir="rtl"] *)::after {
  content: var(--tw-content);
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:focus ~ .rtl\:peer-focus\:left-auto:where([dir="rtl"], [dir="rtl"] *) {
  left: auto;
}

.peer:focus ~ .rtl\:peer-focus\:translate-x-1\/4:where([dir="rtl"], [dir="rtl"] *) {
  --tw-translate-x: 25%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@media (min-width: 640px) {

  .sm\:rtl\:divide-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-x-reverse: 1;
  }
}

.\[\&\:\:-webkit-inner-spin-button\]\:appearance-none::-webkit-inner-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

.\[\&\:\:-webkit-outer-spin-button\]\:appearance-none::-webkit-outer-spin-button {
  -webkit-appearance: none;
          appearance: none;
}

/*!*************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js!./node_modules/svgmap/dist/svgMap.min.css ***!
  \*************************************************************************************************************************************/
/*! svgMap | https://github.com/StephanWagner/svgMap | MIT License | Copyright Stephan Wagner | https://stephanwagner.me */
.svgMap-container,.svgMap-wrapper{position:relative}.svgMap-block-zoom-notice{position:absolute;z-index:2;top:100%;left:0;right:0;bottom:0;background:rgba(0,0,0,.8);pointer-events:none;opacity:0;color:#fff;transition:opacity 250ms}.svgMap-block-zoom-notice-active .svgMap-block-zoom-notice{pointer-events:all;top:0;opacity:1}.svgMap-block-zoom-notice>div{position:absolute;top:50%;left:0;right:0;text-align:center;padding:0 32px;transform:translateY(-50%);font-size:28px}@media (max-width:900px){.svgMap-block-zoom-notice>div{font-size:22px}}.svgMap-map-wrapper{position:relative;width:100%;padding-top:50%;overflow:hidden;background:#d9ecff;color:#111}.svgMap-map-wrapper *{box-sizing:border-box}.svgMap-map-wrapper :focus:not(:focus-visible){outline:0}.svgMap-map-wrapper .svgMap-map-image{display:block;position:absolute;top:0;left:0;width:100%;height:100%;margin:0}.svgMap-map-wrapper .svgMap-map-controls-wrapper{position:absolute;bottom:10px;left:10px;z-index:1;display:flex;overflow:hidden;border-radius:2px;box-shadow:0 0 0 2px rgba(0,0,0,.1)}.svgMap-map-wrapper .svgMap-map-controls-move,.svgMap-map-wrapper .svgMap-map-controls-zoom{display:flex;margin-right:5px;overflow:hidden;background:#fff}.svgMap-map-wrapper .svgMap-map-controls-move:last-child,.svgMap-map-wrapper .svgMap-map-controls-zoom:last-child{margin-right:0}.svgMap-map-wrapper .svgMap-control-button{background-color:transparent;border:none;border-radius:0;color:inherit;font:inherit;line-height:inherit;margin:0;padding:0;overflow:visible;text-transform:none;-webkit-appearance:none;-moz-appearance:none;appearance:none;cursor:pointer;width:30px;height:30px;position:relative}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{content:"";position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);background:#666;transition:background-color 250ms}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before{width:11px;height:3px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-zoom-reset-button::before{width:11px;height:11px;background:0 0;border:2px solid #666}@media (hover:hover){.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:hover:before{background:#111}}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:active:before{background:#111}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:after,.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-disabled:before{background:#ccc}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button.svgMap-zoom-reset-button.svgMap-disabled:before{border:2px solid #ccc;background:0 0}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button{margin:1px 0 1px 1px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-in-button:after{width:3px;height:11px}.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-out-button{margin:1px 1px 1px 0}.svgMap-map-wrapper .svgMap-map-continent-controls-wrapper{position:absolute;top:10px;right:10px;z-index:1;display:flex;border-radius:2px;box-shadow:0 0 0 2px rgba(0,0,0,.1)}.svgMap-map-wrapper .svgMap-country{stroke:#fff;stroke-width:1;stroke-linejoin:round;vector-effect:non-scaling-stroke;transition:fill 250ms,stroke 250ms}.svgMap-map-wrapper .svgMap-country[data-link]{cursor:pointer}@media (hover:hover){.svgMap-map-wrapper .svgMap-country:hover{stroke:#333;stroke-width:1.5}}.svgMap-map-wrapper .svgMap-country.svgMap-active{stroke:#333;stroke-width:1.5}.svgMap-tooltip{box-shadow:0 0 3px rgba(0,0,0,.2);position:absolute;z-index:2;border-radius:2px;background:#fff;transform:translate(-50%,-100%);border-bottom:1px solid #000;display:none;pointer-events:none;min-width:60px}.svgMap-tooltip.svgMap-tooltip-flipped{transform:translate(-50%,0);border-bottom:0;border-top:1px solid #000}.svgMap-tooltip.svgMap-active{display:block}.svgMap-tooltip .svgMap-tooltip-content-container{position:relative;padding:10px 20px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container{text-align:center;margin:2px 0 5px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container.svgMap-tooltip-flag-container-emoji{font-size:50px;line-height:0;padding:25px 0 15px}.svgMap-tooltip .svgMap-tooltip-content-container .svgMap-tooltip-flag-container .svgMap-tooltip-flag{display:block;margin:auto;width:auto;height:32px;padding:2px;background:rgba(0,0,0,.15);border-radius:2px}.svgMap-tooltip .svgMap-tooltip-title{white-space:nowrap;font-size:18px;line-height:28px;padding:0 0 8px;text-align:center}.svgMap-tooltip .svgMap-tooltip-content{white-space:nowrap;text-align:center;font-size:14px;color:#777;margin:-5px 0 0}.svgMap-tooltip .svgMap-tooltip-content table{padding:0;border-spacing:0;margin:auto}.svgMap-tooltip .svgMap-tooltip-content table td{padding:2px 0;text-align:left}.svgMap-tooltip .svgMap-tooltip-content table td span{color:#111}.svgMap-tooltip .svgMap-tooltip-content table td:first-child{padding-right:10px;text-align:right}.svgMap-tooltip .svgMap-tooltip-content table td sup{vertical-align:baseline;position:relative;top:-5px}.svgMap-tooltip .svgMap-tooltip-content .svgMap-tooltip-no-data{padding:2px 0;color:#777;font-style:italic}.svgMap-tooltip .svgMap-tooltip-pointer{position:absolute;top:100%;left:50%;transform:translateX(-50%);overflow:hidden;height:10px;width:30px}.svgMap-tooltip .svgMap-tooltip-pointer:after{content:"";width:20px;height:20px;background:#fff;border:1px solid #000;position:absolute;bottom:6px;left:50%;transform:translateX(-50%) rotate(45deg)}.svgMap-tooltip.svgMap-tooltip-flipped .svgMap-tooltip-pointer{bottom:auto;top:-10px;transform:translateX(-50%) scaleY(-1)}

/*# sourceMappingURL=app.non-encrypted.css.map*/