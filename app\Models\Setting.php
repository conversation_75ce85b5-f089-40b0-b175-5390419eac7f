<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'group',
        'key',
        'value',
        'type',
        'is_public',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
    ];

    /**
     * Get a setting value by key.
     */
    public static function getValue(string $key, string $group = 'general', $default = null)
    {
        $cacheKey = "settings.{$group}.{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $group, $default) {
            $setting = self::where('key', $key)
                          ->where('group', $group)
                          ->first();

            if (!$setting) {
                return $default;
            }

            return self::castValue($setting->value, $setting->type);
        });
    }

    /**
     * Set a setting value.
     */
    public static function setValue(string $key, $value, string $group = 'general', string $type = null, bool $isPublic = false, string $description = null): void
    {
        $setting = self::where('key', $key)
                      ->where('group', $group)
                      ->first();

        if (!$setting) {
            $setting = new self();
            $setting->key = $key;
            $setting->group = $group;
        }

        $setting->value = $value;

        if ($type) {
            $setting->type = $type;
        } elseif (!$setting->type) {
            $setting->type = self::getValueType($value);
        }

        if ($isPublic) {
            $setting->is_public = true;
        }

        if ($description) {
            $setting->description = $description;
        }

        $setting->save();

        // Clear cache
        Cache::forget("settings.{$group}.{$key}");
    }

    /**
     * Cast value to the correct type.
     */
    protected static function castValue($value, string $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'array':
            case 'json':
                return json_decode($value, true);
            case 'object':
                return json_decode($value);
            default:
                return $value;
        }
    }

    /**
     * Get the type of a value.
     */
    protected static function getValueType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        }

        if (is_int($value)) {
            return 'integer';
        }

        if (is_float($value)) {
            return 'float';
        }

        if (is_array($value)) {
            return 'array';
        }

        if (is_object($value)) {
            return 'object';
        }

        return 'string';
    }
}
