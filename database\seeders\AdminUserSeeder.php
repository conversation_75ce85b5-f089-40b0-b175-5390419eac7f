<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Super Admin user
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        // Assign Super Admin role
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        if ($superAdminRole && !$superAdmin->hasRole('Super Admin')) {
            $superAdmin->assignRole('Super Admin');
        }

        // Create regular Admin user
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        // Assign Admin role
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole && !$admin->hasRole('Admin')) {
            $admin->assignRole('Admin');
        }

        // Create test user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Test',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
                'is_active' => true,
            ]
        );

        // Assign User role
        $userRole = Role::where('name', 'User')->first();
        if ($userRole && !$user->hasRole('User')) {
            $user->assignRole('User');
        }

        $this->command->info('Default users created:');
        $this->command->info('Super Admin: <EMAIL> / password');
        $this->command->info('Admin: <EMAIL> / password');
        $this->command->info('User: <EMAIL> / password');
    }
}
