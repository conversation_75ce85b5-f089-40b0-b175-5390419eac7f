<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect width="28" height="20" fill="#E2243B"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H28L14 20L0 0Z" fill="#262626"/>
<mask id="mask1" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H28L14 20L0 0Z" fill="white"/>
</mask>
<g mask="url(#mask1)">
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 11L11.7039 13.5433L11.8787 10.1213L8.45672 10.2961L11 8L8 5.33333L11.8787 6L11.3333 2L14 5.33333L16.6667 2L16.1213 6L20 5.33333L17 8L19.5433 10.2961L16.1213 10.1213L16.2961 13.5433L14 11Z" fill="#FFCF3C"/>
</g>
<g filter="url(#filter1_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 13.3333H28V8H0V13.3333Z" fill="#1984D8"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 20.0002H28V13.3335H0V20.0002Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d" x="8" y="2" width="12" height="11.5433" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
<filter id="filter1_d" x="0" y="8" width="28" height="5.33333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
