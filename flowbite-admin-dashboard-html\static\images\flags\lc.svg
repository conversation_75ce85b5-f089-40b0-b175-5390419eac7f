<svg width="28" height="20" viewBox="0 0 28 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="28" height="20" rx="2" fill="white"/>
<mask id="mask0" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="28" height="20">
<rect width="28" height="20" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0)">
<rect width="28" height="20" fill="#86D7FF"/>
<g filter="url(#filter0_d)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 2.6665L20 17.3332H8L14 2.6665Z" fill="white"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.9999 5.3335L18.6666 17.3335H9.33325L13.9999 5.3335Z" fill="#262626"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14 9.3335L20 17.3335H8L14 9.3335Z" fill="#FFDA57"/>
</g>
<defs>
<filter id="filter0_d" x="8" y="2.6665" width="12" height="15.6667" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
</filter>
</defs>
</svg>
